<?php
/**
 * Unit test for Auth_UserType
 *
 *
 * @package    Framework
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 * @since      File available since 2011-02-16
 */
/**
 * Auth_UserType_Test
 *
 * @package    Framework
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2011 PlusNet
 */
class Auth_UserType_Test extends PHPUnit_Framework_TestCase
{
/**
     * Test for Auth_UserType::getUserTypeIdByhandle
     *
     * @covers Auth_UserType::getUserTypeIdByhandle
     */
    public function testGetUserTypeIdByhandle()
    {
        $dbAdaptor = $this->getMock(
            'Db_Adaptor',
            array('getUserTypeIdByHandle'),
            array('Auth', Db_Manager::DEFAULT_TRANSACTION, false)
        );

        $dbAdaptor->expects($this->once())
            ->method('getUserTypeIdByHandle')
            ->with($this->equalTo("PLUSNET_STAFF"))
            ->will($this->returnValue($this->getMockUserTypeId()));

        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $intUserTypeId = Auth_UserType::getUserTypeIdByhandle("PLUSNET_STAFF");
        $this->assertEquals(1, $intUserTypeId);
    }

    /**
     * Mock Realn id array
     *
     * @return array
     */
    private function getMockUserTypeId()
    {
        return array(
            'intUsertypeId' => 1
        );
    }
}
