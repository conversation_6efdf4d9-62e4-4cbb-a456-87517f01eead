<?php
namespace Plusnet\DirectDebitVerification\Exceptions;

/**
 * DirectDebitVerification InvalidArgument exception
 *
 * @package    DirectDebitVerification
 * @subpackage Exceptions
 * <AUTHOR> Rolling<PERSON> <<EMAIL>>
 */
class DirectDebitVerificationInvalidArgumentException extends DirectDebitVerificationBaseException
{
    /**
     * Returns the log level of this exception
     *
     * @return string Exception level constant from Log_LogData::LOG_LEVEL_*
     */
    public function logLevel()
    {
        return \Log_LogData::LOG_LEVEL_ERROR;
    }
}
