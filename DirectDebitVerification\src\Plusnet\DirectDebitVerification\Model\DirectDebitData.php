<?php
/**
 * Class DirectDebitData
 *
 * <AUTHOR> <mm<PERSON><PERSON><PERSON><PERSON>@plus.net>
 */

namespace Plusnet\DirectDebitVerification\Model;

use Plusnet\DirectDebitVerification\Helpers\AuditLogHelper;

class DirectDebitData
{
    /**
     * Rules to mask fields values when getting json representation of object - mainly for logging purposes
     * Fields (all optional):
     *   - pattern          - regex pattern to mask in callback payload received from BottomLine
     *   - unmaskedHeadSize - numer of characters that should be left unmasked on the beggining of value - when not
     *                        provided AuditLogHelper::DEFAULT_UNMASKED_HEAD_SIZE would be used
     *   - unmaskedTailSize - numer of characters that should be left unmasked on the end of value - when not provided
     *                        AuditLogHelper::DEFAULT_UNMASKED_TAIL_SIZE would be used
     *   - maskCharacter    - character that would be used to mask value - when not provided
     *                        AuditLogHelper::DEFAULT_MASKING_CHARACTER would be used
     * @var array
     */
    public static $maskingRules = [];

    /**
     * Get json representation of object
     *
     * @param bool $masked Flag to mask values following maskingRules array
     *
     * @return false|string
     */
    public function toJson($masked = true)
    {
        $objectVars = get_object_vars($this);
        if ($masked) {
            $class = get_class($this);
            $objectVars = AuditLogHelper::getInstance()->maskArray($objectVars, $class::$maskingRules);
        }

        // Use PHP 5.6 compatible JSON encoding for PHP 8.2
        if (PHP_VERSION_ID >= 80200) {
            return $this->php56CompatibleJsonEncode($objectVars);
        }

        return json_encode($objectVars);
    }

    /**
     * PHP 5.6 compatible JSON encoding for PHP 8.2
     * Ensures that JSON generated by PHP 8.2 can be parsed by PHP 5.6
     *
     * @param mixed $data Data to encode
     * @return string|false JSON string or false on error
     */
    private function php56CompatibleJsonEncode($data)
    {
        // Clean the data for PHP 5.6 compatibility
        $cleanData = $this->cleanDataForPhp56($data);

        // Use only PHP 5.6 compatible JSON flags
        $flags = JSON_UNESCAPED_SLASHES;

        $json = json_encode($cleanData, $flags);

        if ($json === false) {
            // Fallback: try with no flags
            $json = json_encode($cleanData);
        }

        // Additional cleanup for PHP 5.6 compatibility
        if ($json !== false) {
            $json = $this->cleanJsonForPhp56($json);
        }

        return $json;
    }

    /**
     * Clean data to be compatible with PHP 5.6
     *
     * @param mixed $data Data to clean
     * @return mixed Cleaned data
     */
    private function cleanDataForPhp56($data)
    {
        if (is_string($data)) {
            // Ensure proper encoding
            if (!mb_check_encoding($data, 'UTF-8')) {
                $data = mb_convert_encoding($data, 'UTF-8', 'auto');
            }

            // Remove problematic characters
            $data = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $data);

            // Convert high Unicode characters to basic equivalents
            $data = $this->convertUnicodeToBasic($data);

            return $data;
        } elseif (is_array($data)) {
            $cleanArray = [];
            foreach ($data as $key => $value) {
                $cleanKey = $this->cleanDataForPhp56($key);
                $cleanValue = $this->cleanDataForPhp56($value);
                $cleanArray[$cleanKey] = $cleanValue;
            }
            return $cleanArray;
        } elseif (is_object($data)) {
            $cleanObject = clone $data;
            foreach (get_object_vars($cleanObject) as $key => $value) {
                $cleanObject->$key = $this->cleanDataForPhp56($value);
            }
            return $cleanObject;
        }

        return $data;
    }

    /**
     * Clean JSON string for PHP 5.6 compatibility
     *
     * @param string $json JSON string
     * @return string Cleaned JSON string
     */
    private function cleanJsonForPhp56($json)
    {
        // Remove Unicode escape sequences that PHP 5.6 might not handle properly
        $json = preg_replace('/\\\\u([0-9a-fA-F]{4})/', '', $json);

        // Ensure basic ASCII characters only for critical parts
        $json = mb_convert_encoding($json, 'UTF-8', 'UTF-8');

        // Remove any remaining problematic characters
        $json = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $json);

        return $json;
    }

    /**
     * Convert high Unicode characters to basic ASCII equivalents
     *
     * @param string $text Text to convert
     * @return string Converted text
     */
    private function convertUnicodeToBasic($text)
    {
        // Common Unicode to ASCII mappings using Unicode escape sequences
        $replacements = [
            // Smart quotes
            "\u201C" => '"', "\u201D" => '"', "\u2018" => "'", "\u2019" => "'",
            // Dashes
            "\u2013" => '-', "\u2014" => '-',
            // Other common characters
            "\u2026" => '...',
            "\u20AC" => 'EUR',
            "\u00A3" => 'GBP',
            "\u00A9" => '(c)',
            "\u00AE" => '(R)',
            "\u2122" => '(TM)',
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $text);
    }
}
