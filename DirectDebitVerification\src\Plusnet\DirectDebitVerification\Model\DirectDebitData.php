<?php
/**
 * Class DirectDebitData
 *
 * <AUTHOR> <mm<PERSON><PERSON><PERSON><PERSON>@plus.net>
 */

namespace Plusnet\DirectDebitVerification\Model;

use Plusnet\DirectDebitVerification\Helpers\AuditLogHelper;

class DirectDebitData
{
    /**
     * Rules to mask fields values when getting json representation of object - mainly for logging purposes
     * Fields (all optional):
     *   - pattern          - regex pattern to mask in callback payload received from BottomLine
     *   - unmaskedHeadSize - numer of characters that should be left unmasked on the beggining of value - when not
     *                        provided AuditLogHelper::DEFAULT_UNMASKED_HEAD_SIZE would be used
     *   - unmaskedTailSize - numer of characters that should be left unmasked on the end of value - when not provided
     *                        AuditLogHelper::DEFAULT_UNMASKED_TAIL_SIZE would be used
     *   - maskCharacter    - character that would be used to mask value - when not provided
     *                        AuditLogHelper::DEFAULT_MASKING_CHARACTER would be used
     * @var array
     */
    public static $maskingRules = [];

    /**
     * Get json representation of object
     *
     * @param bool $masked Flag to mask values following maskingRules array
     *
     * @return false|string
     */
    public function toJson($masked = true)
    {
        $objectVars = get_object_vars($this);
        if ($masked) {
            $class = get_class($this);
            $objectVars = AuditLogHelper::getInstance()->maskArray($objectVars, $class::$maskingRules);
        }
        return json_encode($objectVars);
    }
}
