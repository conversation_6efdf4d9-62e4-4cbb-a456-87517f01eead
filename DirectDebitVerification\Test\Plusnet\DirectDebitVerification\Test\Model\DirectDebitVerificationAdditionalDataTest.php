<?php
/**
 * DirectDebitVerificationAdditionalDataTest
 *
 * @uses       PHPUnit_Framework_TestCase
 * @package    DirectDebitVerification
 * @subpackage Tests
 * <AUTHOR> Rollings <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Test\Model;

use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationAdditionalData;

class DirectDebitVerificationAdditionalDataTest extends \PHPUnit_Framework_TestCase
{
    /**
     * Tests instantiation
     *
     * @return void
     */
    public function testConstructor()
    {
        $redirectUrl = 'https://my.redirectUrl.com';
        $callbackUrl = 'https://my.callbackUrl.com';
        $customData = 'MyCustomData';

        $extraParameters = [
            'redirectUrl' => $redirectUrl,
            'callbackUrl' => $callbackUrl,
            'customData' => $customData,
            'randomData' => 'boo', // Should not error when passed superfluous data
        ];

        $validationHash = '123abc';
        $additionalData = new DirectDebitVerificationAdditionalData(
            $validationHash,
            $extraParameters
        );
        $this->assertInstanceOf(
            DirectDebitVerificationAdditionalData::class,
            $additionalData
        );
        $this->assertEquals($validationHash, $additionalData->validationHash);
        $this->assertEquals($redirectUrl, $additionalData->redirectUrl);
        $this->assertEquals($callbackUrl, $additionalData->callbackUrl);
        $this->assertEquals($customData, $additionalData->customData);
    }
}
