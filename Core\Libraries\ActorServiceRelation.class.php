<?php
/**
 * Relate a BusinessActor to a Service
 *
 * @see Auth_BusinessActor
 * @see Core_Service
 *
 * @package Core
 * @subpackage Libraries
 * <AUTHOR> <<EMAIL>>
 */
class Core_ActorServiceRelation
{
	/**
	 * Handle from PlusnetSession.tblUserType
	 *
	 */
	const VALID_USER_TYPE = 'PLUSNET_ENDUSER';

	/**
	 * New user type for partner admin users
	 *
	 */
	const VALID_PARTNER_ADMIN_USER_TYPE = 'PLUSNET_PARTNER_ADMIN_USER';

	/**
	 * Exception error code
	 * @todo replace with a specific exception type
	 *
	 */
	const INVALID_USER_TYPE_CODE = 500;

	/**
	 * @var Auth_BusinessActor
	 */
	private $actor;

	/**
	 * @var Core_Service
	 */
	private $service;

	/**
	 * Set up the relationship between the actor and their service
	 *
	 * @param Auth_BusinessActor $actor
	 * @param Core_Service $service
	 */
	public function __construct(Auth_BusinessActor $actor, Core_Service $service)
	{
		if (self::VALID_USER_TYPE != $actor->getUserType() && self::VALID_PARTNER_ADMIN_USER_TYPE != $actor->getUserType())
		{
			throw new InvalidArgumentException('User type of the given actor was not valid', self::INVALID_USER_TYPE_CODE);
		}

		$this->actor   = $actor;
		$this->service = $service;
	}

	/**
	 * Save the relationship to the database
	 *
	 * @param string $strTransaction
	 */
	public function write($strTransaction = Db_Manager::DEFAULT_TRANSACTION)
	{
		$adaptor = $this->getAdaptor($strTransaction);
		$adaptor->insertActorServiceRelation($this->actor->getActorId(), $this->service->getServiceId());
	}

	/**
	 * Get the database adaptor on the relevant transaction, requesting master
	 *
	 * @param string $strTransaction
	 * @return Db_Adaptor
	 */
	private function getAdaptor($strTransaction)
	{
		$adaptor = Db_Manager::getAdaptor('Core', $strTransaction, true);
		return $adaptor;
	}

	/**
	 * Check if a relation exists for the given BusinessActor
	 *
	 * @param int $businessActorId
	 * @return bool
	 */
	public static function existsForActorId($businessActorId)
	{
		$exists = false;

		$adaptor = Db_Manager::getAdaptor('Core');
		$arrRelationRow = $adaptor->getActorServiceRelationByActorId($businessActorId);

		if (!empty($arrRelationRow)) {
			$exists = true;
		}

		return $exists;
	}

	/**
	 * Factory method for use from the legacy code. Sets up the class on a
	 * private transaction and commits it all at the end.
	 * If you're code is already handling transactions properly, there is no
	 * need whatsoever to use this method.
	 *
	 * @param int $businessActorId
	 * @param int $serviceId
	 */
	public static function atomicCreation($businessActorId, $serviceId)
	{
		$strTransaction = 'CREATE_ACTORSERVICE_RELATION';
		Db_Manager::getAdaptor('Core', $strTransaction, true);

		$actor    = Auth_BusinessActor::get($businessActorId, $strTransaction);
		$service  = new Core_Service($serviceId, $strTransaction);
		$relation = new Core_ActorServiceRelation($actor, $service);
		$relation->write($strTransaction);

		Db_Manager::commit($strTransaction);
	}
}