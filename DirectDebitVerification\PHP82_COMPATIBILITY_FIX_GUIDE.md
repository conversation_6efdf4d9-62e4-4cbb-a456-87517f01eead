# DD-Verification-Service PHP 8.2 Compatibility Fix

## Problem Summary

The DD-Verification-Service running on PHP 8.2 was generating JSON responses that PHP 5.6 FrameworkWebApi couldn't parse, resulting in:

```
JsonParseException: Unexpected character ('�' (code 65533 / 0xfffd)): expected a valid value
```

**Communication Flow:**
pn-billing-api → FrameworkWebApi (PHP 5.6) → DD-Verification-Service (PHP 8.2)

## Root Cause

The issue was in the `DirectDebitData::toJson()` method in DD-Verification-Service. When PHP 8.2 generates JSON using `json_encode()`, it produces Unicode characters and encoding that PHP 5.6's `json_decode()` cannot properly parse.

## Solution Implemented

### 1. Enhanced DirectDebitData.php
**File:** `DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php`

- **Modified `toJson()` method**: Now detects PHP 8.2 and uses compatible JSON encoding
- **Added `php56CompatibleJsonEncode()`**: Uses only PHP 5.6 compatible JSON flags
- **Added character cleaning methods**: Removes problematic Unicode and control characters
- **Added Unicode-to-ASCII conversion**: Converts smart quotes, dashes, and special characters

### 2. Created JsonCompatibilityHelper.php
**File:** `DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php`

- **Standalone compatibility helper**: Can be used throughout the application
- **Debug logging**: Provides detailed logging for troubleshooting
- **Comprehensive testing**: Includes test methods for validation

### 3. Created Test Script
**File:** `DirectDebitVerification/test-json-compatibility.php`

- **Comprehensive testing**: Tests all aspects of JSON compatibility
- **Real-world scenarios**: Simulates actual DirectDebit verification data
- **Character encoding tests**: Validates problematic character handling

## Deployment Steps

### Step 1: Backup Current Files
```bash
# Connect to the DD-Verification-Service container
docker exec -it <dd-verification-container> /bin/bash

# Backup the original file
cp /local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php \
   /local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php.backup
```

### Step 2: Apply the Changes
Copy the modified files to the container:

```bash
# From your host machine, copy the files to the container
docker cp DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php \
    <dd-verification-container>:/local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php

docker cp DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php \
    <dd-verification-container>:/local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php

docker cp DirectDebitVerification/test-json-compatibility.php \
    <dd-verification-container>:/local/codebase2005/modules/DirectDebitVerification/test-json-compatibility.php
```

### Step 3: Test the Fix
```bash
# Inside the container, run the test script
docker exec -it <dd-verification-container> /bin/bash
cd /local/codebase2005/modules/DirectDebitVerification
php test-json-compatibility.php
```

**Expected Output:**
- All tests should show "SUCCESS"
- No "ERROR" messages should appear
- JSON should be decodable in all test cases

### Step 4: Restart Services (Graceful)
```bash
# Graceful Apache reload (preferred - no downtime)
docker exec -it <dd-verification-container> kill -USR1 1

# Alternative: Restart PHP-FPM if using it
docker exec -it <dd-verification-container> kill -USR2 $(pgrep php-fpm)
```

### Step 5: Enable Enhanced Logging (Optional)
To help with debugging, you can enable enhanced JSON logging:

```bash
# Inside the container, set debug environment variable
echo 'export DDV_JSON_DEBUG=1' >> /etc/environment

# Or add to your docker-compose environment
# DDV_JSON_DEBUG=1
```

### Step 6: Monitor and Test
```bash
# Monitor DD-Verification-Service logs
docker logs -f <dd-verification-container> | grep -E "(DDV-JSON|JsonParseException|Unexpected character)"

# Test a DirectDebit verification request through pn-billing-api
# Monitor for absence of the original error message
```

## Verification Steps

### 1. Test JSON Compatibility
```bash
# Run the test script and verify all tests pass
docker exec -it <dd-verification-container> php /local/codebase2005/modules/DirectDebitVerification/test-json-compatibility.php
```

### 2. Test Real DirectDebit Flow
1. Initiate a DirectDebit verification request through pn-billing-api
2. Monitor logs for successful completion
3. Verify no "Unexpected character" errors appear

### 3. Check Logs
```bash
# Look for successful JSON processing
docker logs <dd-verification-container> | grep -i "success"

# Check for any remaining JSON errors
docker logs <dd-verification-container> | grep -i "json\|unexpected\|character"
```

## Rollback Plan

If issues occur, rollback by restoring the backup:

```bash
# Restore original file
docker exec -it <dd-verification-container> cp \
    /local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php.backup \
    /local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php

# Restart services
docker exec -it <dd-verification-container> kill -USR1 1
```

## Key Features of the Fix

1. **Automatic Detection**: Only applies compatibility fixes when running on PHP 8.2
2. **Conservative Approach**: Uses only JSON encoding flags compatible with PHP 5.6
3. **Character Cleaning**: Removes Unicode replacement characters and control characters
4. **Smart Conversion**: Converts problematic Unicode characters to ASCII equivalents
5. **Backward Compatible**: Doesn't affect existing functionality on other PHP versions
6. **Comprehensive Testing**: Includes thorough test suite for validation

## Monitoring

After deployment, monitor these indicators:

### Success Indicators
- ✅ Absence of "Unexpected character ('�')" errors in pn-billing-api logs
- ✅ Successful DirectDebit verification completion
- ✅ DD-Verification-Service logs show normal operation
- ✅ Test script shows all "SUCCESS" messages

### Warning Signs
- ❌ JSON parsing errors in any service logs
- ❌ DirectDebit verification failures
- ❌ New character encoding related errors
- ❌ Test script shows "ERROR" messages

## Troubleshooting

### If the fix doesn't work:
1. **Check PHP version**: Ensure DD-Verification-Service is running PHP 8.2
2. **Verify file changes**: Confirm the modified files are in place
3. **Check logs**: Look for any PHP errors or warnings
4. **Run test script**: Use the test script to identify specific issues
5. **Enable debug logging**: Set `DDV_JSON_DEBUG=1` for detailed logging

### If logs still don't appear:
1. **Check log configuration**: Verify `DDV_LOG_PATH=php://stdout` in environment
2. **Check Apache configuration**: Ensure error logging is enabled
3. **Check container logs**: Use `docker logs <container>` to see all output
4. **Enable comprehensive tracing**: Use the debug configuration files provided

This fix should resolve the character encoding issues while maintaining the existing architecture where FrameworkWebApi remains on PHP 5.6 and DD-Verification-Service runs on PHP 8.2.
