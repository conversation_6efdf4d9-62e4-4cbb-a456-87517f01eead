<?php
/**
 * DirectDebitVerification DatabaseHousekeeping class
 *
 * Removes expired Direct Debit Verification data from the database
 *
 * @package    DirectDebitVerification
 * @subpackage Cron
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Cron;

use Plusnet\DirectDebitVerification\Helpers\AuditLogHelper;

class DatabaseHousekeeping
{
    const MAX_BATCH_SIZE = 5000;
    const DEFAULT_BATCH_SIZE = 1000;

    // Output text constants
    const CLEANUP_FAILED = 'Failed to delete expired Direct Debit Verification callbacks due to exception';
    const EXCEPTION_HANDLE_MESSAGE = 'Exception encountered, unable to continue. Exception message follows:';
    const EXIT_EARLY = '[END] Exited housekeeping early';
    const FINISHED_HOUSEKEEPING = '[END] Finished housekeeping';
    const NO_ACTION_NEEDED = 'No expired Direct Debit Verification callback entries found to clean up';
    const NOTHING_TO_CLEANUP = 'Nothing found to process';

    /**
     * Name of the specific transaction to use with database connection
     */
    const DDV_HOUSEKEEPING_TRANSACTION = 'DIRECT_DEBIT_VERIFICATION_HOUSEKEEPING_TRANSACTION';
    private $outputToCli = true;

    /**
     * Main method - run DirectDebitVerification database cleanup / housekeeping
     *
     * @param int $requestedBatchSize The size of batch to use
     *
     * @return void
     */
    public function run($requestedBatchSize = self::DEFAULT_BATCH_SIZE)
    {
        $this->output('[START] Beginning housekeeping');
        $batchSize = $this->validateBatchSize($requestedBatchSize);

        $numberOfRuns = $this->getNumberOfRunsNeeded($batchSize);
        if ($numberOfRuns < 1) {
            $this->output(static::NO_ACTION_NEEDED);
            $this->outputEarlyExit(static::NOTHING_TO_CLEANUP);
            return;
        }

        $cleanupResult = $this->runHousekeepingCleanup($numberOfRuns, $batchSize);
        if ($cleanupResult === false) {
            $this->outputEarlyExit(static::CLEANUP_FAILED);
            return;
        }
        $this->output(static::FINISHED_HOUSEKEEPING);
    }

    /**+
     * Gets a date stamp string for output
     *
     * @return string
     */
    public function getDatestamp()
    {
        $dateTime = new \DateTime;
        return $dateTime->format('Y/m/d H:i:s');
    }

    /**
     * Gets an AuditLogHelper instance
     *
     * @return AuditLogHelper
     */
    public function getAuditLogHelper()
    {
        return AuditLogHelper::getInstance();
    }

    /**
     * Output to stdout
     *
     * @param string $msg   The message to output
     * @param string $level The level / class of the message
     *
     * @return void
     */
    private function output($msg, $level = \Log_LogData::LOG_LEVEL_INFO)
    {
        if ($level == 'ERROR') {
            $level = \Log_LogData::LOG_LEVEL_ERROR;
        }

        $logHelper = $this->getAuditLogHelper();
        $logHelper->log('[DdvHousekeeping] ' . $msg, $level);

        if ($this->outputToCli) {
            $dateTimestamp = $this->getDatestamp();
            print "[$dateTimestamp] [$level] $msg" . PHP_EOL;
        }
    }

    /**
     * Wrapper to output() with some standard values for an early exit
     *
     * @param string $msg The message to output
     *
     * @return void
     */
    private function outputEarlyExit($msg)
    {
        $this->output(static::EXIT_EARLY . ': ' .  $msg);
    }

    /**
     * Helper to get a db_Adaptor object
     *
     * @return \Db_Adaptor
     */
    private function getDbAdaptor()
    {
        return \Db_Manager::getAdaptor(
            'DirectDebitVerification',
            static::DDV_HOUSEKEEPING_TRANSACTION,
            true
        );
    }

    /**
     * Helper function to sanitise inputted batch sizes to sensible values
     *
     * @param int $batchSize Size of batch that was requested
     * @return int
     */
    private function validateBatchSize($batchSize)
    {
        if (empty($batchSize)
            || !is_numeric($batchSize)
            || (int) $batchSize != $batchSize
            || $batchSize > static::MAX_BATCH_SIZE
            || $batchSize < 0
        ) {
            $this->output(
                "Overriding batch size, since requested batch size '" . $batchSize . "' isn't a whole " .
                "numeric value between 1 and MAX_BATCH_SIZE: " . static::MAX_BATCH_SIZE . " inclusive."
            );
            if (is_numeric($batchSize)
                && (int) $batchSize == $batchSize
                && $batchSize > static::MAX_BATCH_SIZE
            ) {
                $this->output(
                    "Setting batch size to defined MAX_BATCH_SIZE of " . static::MAX_BATCH_SIZE . ", since the " .
                    "requested batch size exceeded this."
                );
                $batchSize = static::MAX_BATCH_SIZE;
            } else {
                $this->output(
                    "Setting batch size to defined DEFAULT_BATCH_SIZE of " . static::DEFAULT_BATCH_SIZE . "."
                );
                $batchSize = static::DEFAULT_BATCH_SIZE;
            }
        }
        return (int) $batchSize;
    }

    /**
     * Helper function to get the number of expired DDV requests and calculate how many batches will be needed
     *
     * @param int $batchSize The maximum amount of records which should be processed at once
     *
     * @return int The number of runs required to process all expired records
     */
    private function getNumberOfRunsNeeded($batchSize)
    {
        $step = '[SIZING]';
        $this->output("$step Using batch size of [$batchSize]");
        $this->output("$step Fetching number of expired records needing cleanup");
        try {
            $db = $this->getDbAdaptor();
            $noCallbacksToCleanup = $db->getDDVerificationExpiredRequestsCount();
        } catch (\Exception $e) {
            $this->output(
                "$step " . static::EXCEPTION_HANDLE_MESSAGE . $e->getMessage(),
                'ERROR'
            );
            return 0;
        }
        $this->output("$step [Found] $noCallbacksToCleanup expired records to cleanup");

        // We need sufficient runs to cover all records needing cleanup, so round up.
        return (int) ceil($noCallbacksToCleanup/$batchSize);
    }

    /**
     * Runs batches to delete expired records from the database
     *
     * @param int $numberOfRuns Number of batches to run
     * @param int $batchSize    Size of batch
     *
     * @return false|void
     */
    private function runHousekeepingCleanup($numberOfRuns, $batchSize)
    {
        $step = '[CLEANUP]';
        $limit = new \Db_Limit($batchSize);

        $this->output("$step STARTING batch of $numberOfRuns run(s)");
        for ($i = 1; $i <= $numberOfRuns; $i++) {
            $this->output("$step [Batch $i/$numberOfRuns] Beginning ");
            try {
                $db = $this->getDbAdaptor();
                $db->deleteExpiredDDVerificationCallbacks($limit);
            } catch (\Exception $e) {
                $this->output(
                    "$step [Batch $i/$numberOfRuns] " . static::EXCEPTION_HANDLE_MESSAGE . $e->getMessage(),
                    'ERROR'
                );
                $this->output("$step [Batch $i/$numberOfRuns] Failed! Rolling back all deletes.", 'ERROR');
                \Db_Manager::rollback(static::DDV_HOUSEKEEPING_TRANSACTION);
                return false;
            }

            $this->output("$step [Batch $i/$numberOfRuns] Completed");
        }

        \Db_Manager::commit(static::DDV_HOUSEKEEPING_TRANSACTION);

        $this->output("$step FINISHED all batches");
    }
}
