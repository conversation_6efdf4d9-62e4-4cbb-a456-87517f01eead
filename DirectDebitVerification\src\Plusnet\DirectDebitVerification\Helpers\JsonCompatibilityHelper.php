<?php
/**
 * JSON Compatibility Helper for PHP 8.2 to PHP 5.6 Communication
 * 
 * This helper ensures that JSON generated by PHP 8.2 DD-Verification-Service
 * can be properly parsed by PHP 5.6 FrameworkWebApi
 */

namespace Plusnet\DirectDebitVerification\Helpers;

class JsonCompatibilityHelper
{
    /**
     * Encode JSON in a way that's compatible with PHP 5.6
     * 
     * @param mixed $data Data to encode
     * @param bool $logDebug Whether to log debug information
     * @return string JSON string
     */
    public static function jsonEncodeForPhp56($data, $logDebug = false)
    {
        if ($logDebug) {
            error_log("[DDV-JSON] Original data type: " . gettype($data));
            if (is_string($data)) {
                error_log("[DDV-JSON] Original string length: " . strlen($data));
                error_log("[DDV-JSON] Original string preview: " . substr($data, 0, 100));
            }
        }
        
        // Clean the data for PHP 5.6 compatibility
        $cleanData = self::cleanDataForPhp56($data);
        
        // Use only PHP 5.6 compatible JSON flags
        $flags = JSON_UNESCAPED_SLASHES;
        
        $json = json_encode($cleanData, $flags);
        
        if ($json === false) {
            if ($logDebug) {
                error_log("[DDV-JSON] First encoding failed: " . json_last_error_msg());
            }
            // Fallback: try with no flags
            $json = json_encode($cleanData);
        }
        
        // Additional cleanup for PHP 5.6 compatibility
        if ($json !== false) {
            $json = self::cleanJsonForPhp56($json);
            
            if ($logDebug) {
                error_log("[DDV-JSON] Final JSON length: " . strlen($json));
                error_log("[DDV-JSON] Final JSON preview: " . substr($json, 0, 200));
                
                // Test if the JSON can be decoded
                $testDecode = json_decode($json, true);
                if ($testDecode === null && json_last_error() !== JSON_ERROR_NONE) {
                    error_log("[DDV-JSON] WARNING: Generated JSON cannot be decoded: " . json_last_error_msg());
                } else {
                    error_log("[DDV-JSON] SUCCESS: Generated JSON can be decoded properly");
                }
            }
        } else {
            if ($logDebug) {
                error_log("[DDV-JSON] ERROR: All encoding attempts failed: " . json_last_error_msg());
            }
        }
        
        return $json;
    }
    
    /**
     * Clean data to be compatible with PHP 5.6
     *
     * @param mixed $data Data to clean
     * @return mixed Cleaned data
     */
    public static function cleanDataForPhp56($data)
    {
        if (is_string($data)) {
            // Ensure proper encoding
            if (!mb_check_encoding($data, 'UTF-8')) {
                $data = mb_convert_encoding($data, 'UTF-8', 'auto');
            }
            
            // Remove problematic characters
            $data = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $data);
            
            // Convert high Unicode characters to basic equivalents
            $data = self::convertUnicodeToBasic($data);
            
            return $data;
        } elseif (is_array($data)) {
            $cleanArray = [];
            foreach ($data as $key => $value) {
                $cleanKey = self::cleanDataForPhp56($key);
                $cleanValue = self::cleanDataForPhp56($value);
                $cleanArray[$cleanKey] = $cleanValue;
            }
            return $cleanArray;
        } elseif (is_object($data)) {
            $cleanObject = clone $data;
            foreach (get_object_vars($cleanObject) as $key => $value) {
                $cleanObject->$key = self::cleanDataForPhp56($value);
            }
            return $cleanObject;
        }
        
        return $data;
    }

    /**
     * Clean JSON string for PHP 5.6 compatibility
     *
     * @param string $json JSON string
     * @return string Cleaned JSON string
     */
    public static function cleanJsonForPhp56($json)
    {
        // Remove Unicode escape sequences that PHP 5.6 might not handle properly
        $json = preg_replace('/\\\\u([0-9a-fA-F]{4})/', '', $json);
        
        // Ensure basic ASCII characters only for critical parts
        $json = mb_convert_encoding($json, 'UTF-8', 'UTF-8');
        
        // Remove any remaining problematic characters
        $json = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $json);
        
        return $json;
    }

    /**
     * Convert high Unicode characters to basic ASCII equivalents
     *
     * @param string $text Text to convert
     * @return string Converted text
     */
    public static function convertUnicodeToBasic($text)
    {
        // Common Unicode to ASCII mappings using hex codes
        $replacements = [
            // Smart quotes
            "\xE2\x80\x9C" => '"', // Left double quotation mark
            "\xE2\x80\x9D" => '"', // Right double quotation mark
            "\xE2\x80\x98" => "'", // Left single quotation mark
            "\xE2\x80\x99" => "'", // Right single quotation mark
            // Dashes
            "\xE2\x80\x93" => '-', // En dash
            "\xE2\x80\x94" => '-', // Em dash
            // Other common characters
            "\xE2\x80\xA6" => '...', // Horizontal ellipsis
            "\xE2\x82\xAC" => 'EUR', // Euro sign
            "\xC2\xA3" => 'GBP',     // Pound sign
            "\xC2\xA9" => '(c)',     // Copyright sign
            "\xC2\xAE" => '(R)',     // Registered sign
            "\xE2\x84\xA2" => '(TM)', // Trade mark sign
        ];
        
        return str_replace(array_keys($replacements), array_values($replacements), $text);
    }
    
    /**
     * Test JSON compatibility between PHP versions
     * 
     * @param mixed $testData Data to test
     * @return array Test results
     */
    public static function testJsonCompatibility($testData)
    {
        $results = [
            'original_json' => json_encode($testData),
            'compatible_json' => self::jsonEncodeForPhp56($testData, true),
            'original_decodable' => false,
            'compatible_decodable' => false,
            'original_error' => '',
            'compatible_error' => ''
        ];
        
        // Test original JSON
        $originalDecoded = json_decode($results['original_json'], true);
        if ($originalDecoded !== null || json_last_error() === JSON_ERROR_NONE) {
            $results['original_decodable'] = true;
        } else {
            $results['original_error'] = json_last_error_msg();
        }
        
        // Test compatible JSON
        $compatibleDecoded = json_decode($results['compatible_json'], true);
        if ($compatibleDecoded !== null || json_last_error() === JSON_ERROR_NONE) {
            $results['compatible_decodable'] = true;
        } else {
            $results['compatible_error'] = json_last_error_msg();
        }
        
        return $results;
    }
}
