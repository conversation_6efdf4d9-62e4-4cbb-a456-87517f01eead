<?php

class testSessionManager extends PHPUnit_Framework_TestCase
{
    /**
     * Tests getting soap client for the first time fetches a brand new instance
     *
     * @covers Auth_SessionManager
     * @covers Auth_SessionManager::getSoapClient
     *
     * @return void
     */
    public function testGetSoapClientFresh()
    {
        // Mock of a soap service, so just extend stdClass and provide the magic methods
        $sessionMock = $this->getMock('stdClass', array(), array());
        BusTier_BusTier::setClient('session', $sessionMock);

        $authSessionManager = new Auth_SessionManager();
        $this->assertEquals($sessionMock, $authSessionManager->getSoapClient());
    }

    /**
     * Tests getting soap client subsequent times fetches the same instance
     *
     * @covers Auth_SessionManager::getSoapClient
     *
     * @return void
     */
    public function testGetSoapClientFromCache()
    {
        // Mock of a soap service, so just extend stdClass and provide the magic methods
        $sessionMock = $this->getMock('stdClass', array(), array());
        $newSessionMock = $this->getMock('stdClass', array(), array());

        $sessionMock->someAttr = 'blah';

        // First prove that these two things are not the same
        $this->assertNotEquals($sessionMock, $newSessionMock);

        BusTier_BusTier::setClient('session', $sessionMock);

        $authSessionManager = new Auth_SessionManager();
        $freshSoapClient = $authSessionManager->getSoapClient();

        // Should be our mock class
        $this->assertEquals($sessionMock, $freshSoapClient);

        // When we try again we should get exactly the same back, and not the 'new'SessionMock
        BusTier_BusTier::setClient('session', $newSessionMock);
        $cacheSoapClient = $authSessionManager->getSoapClient();

        $this->assertEquals($freshSoapClient, $cacheSoapClient);
        $this->assertNotEquals($newSessionMock, $cacheSoapClient);
    }

    /**
     * Tests retreiving an Auth_Session from the soap client. This is a little
     * shakey since we're really just checking that we get the value back that
     * we assigned to the mock.
     *
     * @covers Auth_SessionManager::getBusinessTierSession
     *
     * @return void
     */
    public function testGetBusinessTierSession()
    {
        $username = 'TestUser';
        $password = 'TestPassword';
        $realm    = 'test.rlm';
        $dataset  = 'TEST';

        $session = new Auth_Session();
        $session->actorId = 12345;

        // Mock of a soap service, so just extend stdClass and provide the magic methods
        $sessionMock = $this->getMock(
            'stdClass',
            array('getSessionID'),
            array()
        );
        $sessionMock
            ->expects($this->once())
            ->method('getSessionID')
            ->with($username, $realm, $dataset, $password)
            ->will($this->returnValue($session));
        BusTier_BusTier::setClient('session', $sessionMock);

        $authSessionManager = new Auth_SessionManager();
        $this->assertEquals(
            $session,
            $authSessionManager->getBusinessTierSession($username, $password, $realm, $dataset)
        );
    }

    /**
     * Tests refreshing the Business Tier Session when the business tier
     * session check returns false.
     *
     * @covers Auth_SessionManager::refreshBusinessTierSession
     *
     * @return void
     */
    public function testRefreshSessionManagerSessionNotValid()
    {
        // Mock of a soap service, so just extend stdClass and provide the magic methods
        $sessionMock = $this->getMock(
            'stdClass',
            array('checkSessionValid'),
            array()
        );
        $sessionMock
            ->expects($this->once())
            ->method('checkSessionValid')
            ->will($this->returnValue(false));
        BusTier_BusTier::setClient('session', $sessionMock);

        $session = new Auth_Session();
        $session->actorId = 12345;

        $authSessionManager = new Auth_SessionManager();

        $this->assertFalse($authSessionManager->refreshBusinessTierSession($session));
    }

    /**
     * Tests refreshing the Business Tier Session when the business tier
     * session check returns true
     *
     * @covers Auth_SessionManager::refreshBusinessTierSession
     *
     * @return void
     */
    public function testRefreshSessionManagerSessionIsValid()
    {
        // Mock of a soap service, so just extend stdClass and provide the magic methods
        $sessionMock = $this->getMock(
            'stdClass',
            array('checkSessionValid'),
            array()
        );
        $sessionMock
            ->expects($this->once())
            ->method('checkSessionValid')
            ->will($this->returnValue(true));
        BusTier_BusTier::setClient('session', $sessionMock);

        $session = new Auth_Session();
        $session->actorId = 12345;

        $authSessionManager = new Auth_SessionManager();

        $this->assertTrue($authSessionManager->refreshBusinessTierSession($session));
    }

    /**
     * Tests refreshing the Business Tier Session when the session refresh has
     * not rotted
     *
     * @covers Auth_SessionManager::__construct
     * @covers Auth_SessionManager::refreshBusinessTierSession
     * @covers Auth_SessionManager::__destruct
     *
     * @return void
     */
//    public function testRefreshSessionManagerSessionIsFresh()
//    {
//        $this->markTestIncomplete('This test is broken and needs fixing.');
//        // Mock of a soap service, so just extend stdClass and provide the magic methods
//        $sessionMock = $this->getMock(
//            'stdClass',
//            array('checkSessionValid'),
//            array()
//        );
//        // We should not expect this to be called, but we set it up anyway as
//        // leaving the session unmocked could cause unexpected results (fatal)
//        $sessionMock
//            ->expects($this->never())
//            ->method('checkSessionValid')
//            ->will($this->returnValue(false));
//        BusTier_BusTier::setClient('session', $sessionMock);
//
//        $session = new Auth_Session();
//        $session->actorId = 12345;
//
//        // Attempt to fake up some session settings
//        if (!session_id()) {
//            $iso = session_start();
//        }
//        $_SESSION['smRefresh'][12345] = time();
//
//        $authSessionManager = new Auth_SessionManager();
//        $this->assertTrue($authSessionManager->refreshBusinessTierSession($session));
//
//        if ($iso) {
//            session_destroy();
//        } else {
//            unset($_SESSION['smRefresh']);
//        }
//    }
}
