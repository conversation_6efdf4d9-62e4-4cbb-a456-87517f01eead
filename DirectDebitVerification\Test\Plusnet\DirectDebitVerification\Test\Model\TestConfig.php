<?php
/**
 * TestConfig
 *
 * @uses       PHPUnit_Framework_TestCase
 * @package    DirectDebitVerification
 * @subpackage Tests
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Test\Model;

class TestConfig
{
    /**
     * @var string The path to a local file containing json
     */
    const LOCAL_TEST_CONFIG_FILE = __DIR__ . '/../Config/directDebitVerificationConfigTestConfig.json';

    /**
     * <PERSON><PERSON> decoded array of the content in the file path held in above constant: LOCAL_TEST_CONFIG_FILE
     *
     * @var array
     */
    const TEST_CONFIG_ARRAY = [
        'namedConfigIdentifier' => 'developmentEnvironmentServer-to-bottomlineTestPlatform',
        'webFormProviderBaseURI' => 'https://webforms.cat.uk.pt-x.com/forms/plusnetbb',
        "webFormCallbackBaseURI" => "https://portal.plus.net",
        'webFormSharedSecret' => 'FB62C4B3A42D42F2B0579A05796706A5',
        'dataKey' => '352fd782c543063bd8db4e0890cc0c21',
        'maxWaitTimeSec' => 15,
        'passingNameScore' => 7,
        'passingAddressScore' => 7
    ];

    /**
     * @var string The path to a local file containing json pointing to the mock service
     */
    const LOCAL_TEST_CONFIG_FILE_MOCK = __DIR__ . '/../Config/directDebitVerificationConfigTestMockConfig.json';

    /**
     * Json decoded array of the content in the file path held in above constant: LOCAL_TEST_CONFIG_FILE_MOCK
     *
     * @var array
     */
    const TEST_CONFIG_MOCK_ARRAY = [
        'namedConfigIdentifier' => 'developmentEnvironmentServer-to-mockTestPlatform',
        'webFormProviderBaseURI' => 'https://webforms.cat.uk.pt-x.com/forms/plusnetbb/mock',
        "webFormCallbackBaseURI" => "https://portal.plus.net",
        'webFormSharedSecret' => 'FB62C4B3A42D42F2B0579A05796706A5',
        'dataKey' => '352fd782c543063bd8db4e0890cc0c21',
        'maxWaitTimeSec' => 15,
        'passingNameScore' => 7,
        'passingAddressScore' => 7
    ];
}
