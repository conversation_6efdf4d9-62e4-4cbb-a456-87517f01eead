# DirectDebitVerification PHP 8.2 Compatibility Solution

## Problem Analysis

You were experiencing a character encoding issue between:
- **pn-billing-api** → **FrameworkWebApi (PHP 5.6)** → **DD-Verification-Service (PHP 8.2)**

The error was:
```
JsonParseException: Unexpected character ('�' (code 65533 / 0xfffd)): expected a valid value
```

This occurred because PHP 8.2's JSON encoding produces characters that PHP 5.6 cannot parse properly.

## Root Cause

The issue was in the `SerialiserBase::getEncodedObjectAsJson()` method in FrameworkWebApi. When PHP 8.2 serializes objects and converts them to JSON, it uses different encoding defaults and Unicode handling compared to PHP 5.6, resulting in the Unicode replacement character (�) that breaks JSON parsing.

## Solution Implemented

I've implemented a comprehensive compatibility layer that addresses the encoding issues:

### 1. Enhanced SerialiserBase.php
- **Modified `getEncodedObjectAsJson()`**: Now detects PHP 8.2 and uses compatible JSON encoding
- **Added `php56CompatibleJsonEncode()`**: Uses only PHP 5.6 compatible JSON flags
- **Added character cleaning methods**: Removes problematic Unicode and control characters
- **Added Unicode-to-ASCII conversion**: Converts smart quotes, dashes, and special characters

### 2. Updated SerialiserController.php
- **Enhanced JSON decoding**: Uses `EncodingHelper::safeJsonDecode()` for PHP 8.2
- **Added error handling**: Better logging and fallback mechanisms
- **Maintained backward compatibility**: Still works with PHP 5.6 systems

### 3. Improved Configuration
- **Updated php82-compatibility.ini**: Uses conservative JSON encoding flags
- **Added debugging options**: Better error logging for troubleshooting

## Key Features of the Fix

1. **Automatic Detection**: Detects PHP 8.2 and applies fixes automatically
2. **Conservative Encoding**: Uses only JSON flags compatible with PHP 5.6
3. **Character Sanitization**: Removes Unicode replacement characters and control characters
4. **Error Logging**: Provides detailed debugging information
5. **Zero Downtime**: Can be deployed without rebuilding Docker images

## Deployment Instructions

### Quick Deployment (Recommended)
Since you prefer testing on running containers rather than rebuilding:

1. **Copy the modified files** to your running DD-Verification-Service container
2. **Restart Apache gracefully**:
   ```bash
   docker exec -it <container-name> kill -USR1 1
   ```
3. **Test the fix** by running a DirectDebit verification request
4. **Monitor logs** for the absence of the original error

### Testing
Run the provided test script to verify the fix:
```bash
docker exec -it <container-name> php /path/to/FrameworkWebApi/test-php82-compatibility.php
```

## Expected Results

After applying this fix:
- ✅ No more "Unexpected character ('�')" errors
- ✅ DirectDebit verification requests complete successfully  
- ✅ JSON responses are properly parsed by PHP 5.6 FrameworkWebApi
- ✅ Communication flow works: pn-billing-api → FrameworkWebApi → DD-Verification-Service

## Files Modified

1. `FrameworkWebApi/library/SerialiserBase.php` - Core JSON encoding fix
2. `FrameworkWebApi/application/controllers/SerialiserController.php` - Enhanced request handling
3. `FrameworkWebApi/conf/php82-compatibility.ini` - Configuration updates
4. `FrameworkWebApi/test-php82-compatibility.php` - Test script (new)
5. `FrameworkWebApi/PHP82_COMPATIBILITY_DEPLOYMENT_GUIDE.md` - Deployment guide (new)

## Why This Solution Works

1. **Addresses the Root Cause**: Fixes JSON encoding differences between PHP versions
2. **Maintains Compatibility**: Works with existing PHP 5.6 FrameworkWebApi
3. **Conservative Approach**: Uses only well-supported JSON encoding features
4. **Comprehensive**: Handles multiple types of encoding issues (Unicode, control characters, etc.)
5. **Testable**: Includes test script to verify the fix works

## Monitoring

After deployment, monitor for:
- Absence of JSON parsing errors in DD-Verification-Service logs
- Successful completion of DirectDebit verification requests
- No new errors in pn-billing-api logs

This solution should resolve your character encoding issues while maintaining the existing architecture where FrameworkWebApi remains on PHP 5.6 and DD-Verification-Service runs on PHP 8.2.
