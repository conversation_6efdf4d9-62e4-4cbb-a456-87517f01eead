# DirectDebitVerification PHP 8.2 Compatibility Solution

## Problem Analysis

You were experiencing a character encoding issue in the DirectDebit verification flow:

**Test Environment**: dd-verification-service runs on **PHP 8.0** ✅ (works fine)
**Live Environment**: dd-verification-service runs on **PHP 8.2** ❌ (fails with Unicode issues)

The error was:
```
JsonParseException: Unexpected character ('�' (code 65533 / 0xfffd)): expected a valid value
```

## Root Cause Identified

The issue occurs because PHP 8.2 introduced changes in JSON encoding that create Unicode characters incompatible with PHP 5.6 FrameworkWebApi.

**Communication flow:**
orders-api → pn-billing-api → FrameworkWebApi (PHP 5.6) → dd-verification-service (PHP 8.2) → FrameworkWebApi (PHP 5.6) → pn-billing-api

When dd-verification-service (PHP 8.2) generates JSON responses, they contain Unicode characters that FrameworkWebApi (PHP 5.6) cannot parse properly.

## Solution Implemented

The fix is implemented in **dd-verification-service** to make its PHP 8.2 JSON output compatible with PHP 5.6 FrameworkWebApi:

### Enhanced DirectDebitData.php
**File:** `DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php`

- **Modified `toJson()` method**: Detects PHP 8.2 and uses compatibility mode
- **Added `php56CompatibleJsonEncode()`**: Uses only PHP 5.6 compatible JSON flags
- **Added `cleanDataForPhp56()`**: Removes problematic Unicode and control characters
- **Added `convertUnicodeToBasic()`**: Converts smart quotes, dashes, and special characters to ASCII
- **Added `cleanJsonForPhp56()`**: Final cleanup of JSON string for PHP 5.6 compatibility

## Key Features of the Fix

1. **Automatic Detection**: Only applies compatibility fixes when running on PHP 8.2
2. **Conservative Encoding**: Uses only JSON flags compatible with PHP 5.6
3. **Character Sanitization**: Removes Unicode replacement characters and control characters
4. **Smart Conversion**: Converts problematic Unicode characters to ASCII equivalents
5. **Zero Downtime**: Can be deployed without rebuilding Docker images
6. **Backward Compatible**: Doesn't affect existing functionality

## Manual Deployment Instructions

Since you cannot run deployment scripts on LIVE, here are the manual steps:

### Files to Deploy
Copy these files to your LIVE dd-verification-service container:

1. **DirectDebitData.php** (main fix)
   - **From:** `DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php`
   - **To:** `/local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php`

2. **JsonCompatibilityHelper.php** (helper class)
   - **From:** `DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php`
   - **To:** `/local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php`

### Deployment Steps
```bash
# 1. Backup original files
docker exec -it <container-name> cp /local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php /tmp/DirectDebitData.php.backup

# 2. Copy new files using docker cp
docker cp DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php <container-name>:/local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php

# 3. Create helpers directory and copy helper
docker exec -it <container-name> mkdir -p /local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers
docker cp DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php <container-name>:/local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php

# 4. Restart Apache gracefully
docker exec -it <container-name> kill -USR1 1
```

## Why No Logs Appeared in DD-Verification-Service on LIVE

The reason you saw no logs in DD-Verification-Service on LIVE is because:

1. **PHP 8.2 compatibility issues** were causing silent failures in dd-verification-service
2. **Test environment uses PHP 8.0** (which works fine) vs **Live uses PHP 8.2** (which has the issue)
3. **JSON encoding failures** in PHP 8.2 were preventing proper logging and response generation

After applying this fix, you should start seeing logs in dd-verification-service on LIVE.

## Expected Results

After applying this fix:
- ✅ No more "Unexpected character ('�')" errors in pn-billing-api
- ✅ DirectDebit verification requests complete successfully
- ✅ JSON generated by dd-verification-service (PHP 8.2) is properly parsed by FrameworkWebApi (PHP 5.6)
- ✅ Logs appear in dd-verification-service on LIVE (they should start working)
- ✅ Communication flow works: pn-billing-api → FrameworkWebApi → dd-verification-service → FrameworkWebApi → pn-billing-api

## Files Modified (dd-verification-service)

1. `DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php` - Core JSON encoding fix
2. `DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php` - Compatibility helper

## Why This Solution Works

1. **Addresses the Root Cause**: Fixes JSON encoding in dd-verification-service where PHP 8.2 generates incompatible JSON
2. **Correct Architecture Understanding**: Fixes the issue in dd-verification-service (PHP 8.2) to be compatible with FrameworkWebApi (PHP 5.6)
3. **Conservative Approach**: Uses only well-supported JSON encoding features compatible with PHP 5.6
4. **Comprehensive**: Handles multiple types of encoding issues (Unicode, control characters, smart quotes, etc.)
5. **Automatic Detection**: Automatically detects PHP 8.2 and applies compatibility mode
6. **Backward Compatible**: Still works with older PHP versions

## Monitoring

After deployment, monitor for:
- Absence of JSON parsing errors in pn-billing-api logs
- Successful completion of DirectDebit verification requests
- Logs appearing in dd-verification-service on LIVE (they should start working)
- No new character encoding related errors

## Key Insight

The critical insight is:
- **Test environment**: dd-verification-service runs on **PHP 8.0** ✅ (works fine)
- **Live environment**: dd-verification-service runs on **PHP 8.2** ❌ (fails with Unicode issues)

PHP 8.2 introduced JSON encoding changes that create Unicode characters incompatible with PHP 5.6 FrameworkWebApi. This fix makes PHP 8.2 generate JSON that PHP 5.6 can parse properly.
