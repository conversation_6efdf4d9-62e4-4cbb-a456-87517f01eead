<?php
/**
 *
 *
 * <AUTHOR> <mm<PERSON><PERSON><PERSON><PERSON>@plus.net>
 */

namespace Plusnet\Housemoves\Test\Plusnet\DirectDebitVerification\Model;

use Plusnet\DirectDebitVerification\Model\DirectDebitData;

class TestDirectDebitData extends DirectDebitData
{
    public $testField;
}

class DirectDebitDataTest extends \PHPUnit_Framework_TestCase
{
    /**
     * Test toJson and maskArray works properly
     * @param string|array $testFieldValue Test field value
     * @param array        $maskingRules   Masking rules
     * @param bool         $shallMask      Shall mask flag
     * @param string       $expectedResult Expected result
     *
     * @dataProvider provideDataForTestToJsonWorksProperly
     * @return void
     */
    public function testToJsonWorksProperly($testFieldValue, $maskingRules, $shallMask, $expectedResult)
    {
        $testDirectDebitData = new TestDirectDebitData();
        $testDirectDebitData::$maskingRules = $maskingRules;
        $testDirectDebitData->testField = $testFieldV<PERSON>;

        $this->assertEquals($expectedResult, $testDirectDebitData->toJson($shallMask));
    }

    /**
     * Provide data for testToJsonAndMaskArrayWorksProperly
     *
     * @return array
     */
    public function provideDataForTestToJsonWorksProperly()
    {
        $testFieldValue = 'some value';
        $testFieldArrayValue = [
            'level1' => 'some level1 value'
        ];
        $testFieldNestedArrayValue = [
            'level1' => [
                'level2' => 'some level2 value'
            ]
        ];
        $jsonTenmplate = '{"testField":"%s"}';
        $jsonTenmplateWithArray = '{"testField":%s}';
        return [
            'No masking rules - mask - unmasked value in json'                                       => [
                'testFieldValue' => $testFieldValue,
                'maskingRules'   => [],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplate, $testFieldValue)
            ],
            'Full rules provided - mask - masked value with provided rules'                          => [
                'testFieldValue' => $testFieldValue,
                'maskingRules'   => [
                    'testField' => [
                        'unmaskedHeadSize' => 1,
                        'unmaskedTailSize' => 1,
                        'maskCharacter'    => '#'
                    ]
                ],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplate, 's########e')
            ],
            'Empty rules provided - mask - masked value with all default rules'                      => [
                'testFieldValue' => $testFieldValue,
                'maskingRules'   => [
                    'testField' => []
                ],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplate, 'so******ue')
            ],
            'Full rules provided empty field vaklue - mask - empty value in json'                    => [
                'testFieldValue' => '',
                'maskingRules'   => [
                    'testField' => [
                        'unmaskedHeadSize' => 1,
                        'unmaskedTailSize' => 1,
                        'maskCharacter'    => '#'
                    ]
                ],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplate, '')
            ],
            'Full rules provided - not mask - unmasked value in json'                                => [
                'testFieldValue' => $testFieldValue,
                'maskingRules'   => [
                    'testField' => [
                        'unmaskedHeadSize' => 1,
                        'unmaskedTailSize' => 1,
                        'maskCharacter'    => '#'
                    ]
                ],
                'shallMask'      => false,
                'expectedResult' => sprintf($jsonTenmplate, $testFieldValue)
            ],
            'No unmaskedHeadSize provided - mask - masked value with default unmaskedHeadSize = 2'   => [
                'testFieldValue' => $testFieldValue,
                'maskingRules'   => [
                    'testField' => [
                        'unmaskedTailSize' => 1,
                        'maskCharacter'    => '#'
                    ]
                ],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplate, 'so#######e')
            ],
            'No unmaskedTailSize provided - mask - masked value with default unmaskedTailSize = 2'   => [
                'testFieldValue' => $testFieldValue,
                'maskingRules'   => [
                    'testField' => [
                        'unmaskedHeadSize' => 1,
                        'maskCharacter'    => '#'
                    ]
                ],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplate, 's#######ue')
            ],
            'No maskCharacter provided - mask - masked value with default maskCharacter = *'         => [
                'testFieldValue' => $testFieldValue,
                'maskingRules'   => [
                    'testField' => [
                        'unmaskedHeadSize' => 1,
                        'unmaskedTailSize' => 1
                    ]
                ],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplate, 's********e')
            ],
            'Array value - no masking rules - mask - unmasked value in json'                         => [
                'testFieldValue' => $testFieldArrayValue,
                'maskingRules'   => [],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplateWithArray, '{"level1":"some level1 value"}')
            ],
            'Array value - masking rules provided - mask - masked array value in json'               => [
                'testFieldValue' => $testFieldArrayValue,
                'maskingRules'   => [
                    'level1' => [
                        'unmaskedHeadSize' => 1,
                        'unmaskedTailSize' => 1,
                        'maskCharacter'    => '*'

                    ]
                ],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplateWithArray, '{"level1":"s***************e"}')
            ],
            'Array value - masking rules provided - not mask - unmasked value in json'               => [
                'testFieldValue' => $testFieldArrayValue,
                'maskingRules'   => [
                    'level1' => [
                        'unmaskedHeadSize' => 1,
                        'unmaskedTailSize' => 1,
                        'maskCharacter'    => '*'

                    ]
                ],
                'shallMask'      => false,
                'expectedResult' => sprintf($jsonTenmplateWithArray, '{"level1":"some level1 value"}')
            ],
            'Nested array value - masking rules provided - mask - masked nested array value in json' => [
                'testFieldValue' => $testFieldNestedArrayValue,
                'maskingRules'   => [
                    'level2' => [
                        'unmaskedHeadSize' => 1,
                        'unmaskedTailSize' => 1,
                        'maskCharacter'    => '*'

                    ]
                ],
                'shallMask'      => true,
                'expectedResult' => sprintf($jsonTenmplateWithArray, '{"level1":{"level2":"s***************e"}}')
            ],
            'Nested array value - masking rules provided - not mask - unmasked value in json'        => [
                'testFieldValue' => $testFieldNestedArrayValue,
                'maskingRules'   => [
                    'level2' => [
                        'unmaskedHeadSize' => 1,
                        'unmaskedTailSize' => 1,
                        'maskCharacter'    => '*'

                    ]
                ],
                'shallMask'      => false,
                'expectedResult' => sprintf($jsonTenmplateWithArray, '{"level1":{"level2":"some level2 value"}}')
            ]
        ];
    }
}
