# PHP 8.2 Compatibility Configuration for FrameworkWebApi
# This file addresses character encoding and JSON handling issues

# Character encoding settings
default_charset = "UTF-8"
internal_encoding = "UTF-8"
input_encoding = "UTF-8"
output_encoding = "UTF-8"

# JSON encoding settings for PHP 8.2 - Use conservative flags for PHP 5.6 compatibility
json.encode_options = 64
# 64 = JSON_UNESCAPED_SLASHES only (compatible with PHP 5.6)

# Serialization settings
serialize_precision = -1
unserialize_max_depth = 4096

# Error handling for debugging
log_errors = On
error_log = php://stdout

# Memory settings for large serialized objects
memory_limit = 512M

# Input handling
max_input_vars = 10000
max_input_time = 300

# Enable PHP 8.2 compatibility mode for DirectDebitVerification
# This environment variable enables the compatibility fixes
auto_prepend_file = ""

# Custom error handling for JSON parsing issues
display_errors = Off
log_errors_max_len = 1024

# Output buffering to handle encoding issues
output_buffering = 4096
output_handler = mb_output_handler

# Multibyte string settings
mbstring.language = UTF-8
mbstring.internal_encoding = UTF-8
mbstring.http_input = auto
mbstring.http_output = UTF-8
mbstring.encoding_translation = On
mbstring.detect_order = auto
mbstring.substitute_character = none
