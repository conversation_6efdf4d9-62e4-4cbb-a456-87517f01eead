<?php
/**
 * Class AuthTest
 *
 * @package    Framework
 * @subpackage Auth
 * <AUTHOR> <<EMAIL>>
 * @since      October 2014
 */

require_once 'Filter_TestCase.php';

/**
 * Class AuthTest
 *
 * @package    Framework
 * @subpackage Auth
 * <AUTHOR> <<EMAIL>>
 * @since      October 2014
 */
class FaultsFilterTest extends Filter_TestCase
{
    /**
     * Tests that the filter indicates that a redirect a required is if there are faults
     *
     * @covers Auth_FaultsFilter
     * @return void
     */
    public function testShouldIndicateRedirectRequiredOnFaults()
    {
        $mockHttpRequest = $this->createFullyMockedObject('Mvc_HttpRequest');
        $mockRequestRequirements = $this->createFullyMockedObject('Mvc_RequestRequirements');
        $mockLogin = $this->createMockLoginWithChildAccounts();
        $this->setupMockDbAdaptorWithFaultCountOf(1);

        $this->assertTrue(
            Auth_FaultsFilter::checkFilter($mockHttpRequest, $mockRequestRequirements, $mockLogin)
        );
    }

    /**
     * Tests that filter indicates redirect is not required if there are no faults
     *
     * @covers Auth_FaultsFilter
     * @return void
     */
    public function testShouldIndicateRedirectNotRequiredWhenThereAreNoFaults()
    {
        $mockHttpRequest = $this->createFullyMockedObject('Mvc_HttpRequest');
        $mockRequestRequirements = $this->createFullyMockedObject('Mvc_RequestRequirements');
        $mockLogin = $this->createMockLoginWithChildAccounts();
        $this->setupMockDbAdaptorWithFaultCountOf(0);

        $this->assertFalse(
            Auth_FaultsFilter::checkFilter($mockHttpRequest, $mockRequestRequirements, $mockLogin)
        );
    }

    /**
     * Sets up mock Db_Adaptor and sets number of faults to report
     *
     * @param int $faultCount Number of faults to report
     *
     * @return void
     */
    protected function setupMockDbAdaptorWithFaultCountOf($faultCount)
    {
        $faults = array();
        while ($faultCount > 0) {
            $faults[] = $faultCount--;
        }

        $this->setUpMockDbAdaptor(
            'Auth',
            array(
                'getLoginComponents' => array(1, array(1, 2)),
                'getUnfinishedFaults' => array(1, $faults)
            )
        );
    }
}
