<?php
/**
 * <AUTHOR> <<EMAIL>>
 */
class Auth_SessionRestorerHelperTest extends \PHPUnit_Framework_TestCase
{
    /**
     * @var Auth_SessionRestorer|\Mockery\LegacyMockInterface|\Mockery\MockInterface
     */
    private $mockSessionRestorer;

    /**
     * @var Auth_SessionRestorerHelper|\Mockery\Mock
     */
    private $test;


    /**
     * @return void
     */
    public function setup()
    {
        $this->mockSessionRestorer = Mockery::mock(Auth_SessionRestorer::class);

        $this->test = Mockery::mock(Auth_SessionRestorerHelper::class)
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();

        $this->test
            ->shouldReceive('getSessionRestorer')
            ->once()
            ->andReturns($this->mockSessionRestorer);

        $this->test->__construct();
    }

    /**
     * @return void
     */
    public function tearDown()
    {
        Auth_Auth::reset();
        Mockery::close();
    }

    /**
     * @test
     */
    public function shouldReturnTrueAndStoreLoginIfHeaderSetAndSessionSet()
    {
        $mockLogin = Mockery::mock(Auth_Login::class);
        $headers = [
            'header-key' => 'header-value'
        ];

        $this->mockSessionRestorer
            ->shouldReceive('loginToBusinessTier')
            ->once()
            ->with('header-value')
            ->andReturnTrue();

        Auth_Auth::setCurrentLogin($mockLogin);

        $result = $this->test->setSessionFromRequestHeaders($headers, 'header-key');

        $this->assertTrue($result);
        $this->assertSame($mockLogin, $this->test->getLoginObject());
    }

    /**
     * @test
     */
    public function shouldReturnFalseAndHaveNoLoginIfHeaderSetAndSessionNotSet()
    {
        $mockLogin = Mockery::mock(Auth_Login::class);
        $headers = [
            'header-key' => 'header-value'
        ];

        $this->mockSessionRestorer
            ->shouldReceive('loginToBusinessTier')
            ->once()
            ->with('header-value')
            ->andReturnFalse();

        // Set this to show the object isn't retrieved by the helper
        Auth_Auth::setCurrentLogin($mockLogin);

        $result = $this->test->setSessionFromRequestHeaders($headers, 'header-key');

        $this->assertFalse($result);
        $this->assertEmpty($this->test->getLoginObject());
    }

    /**
     * @test
     */
    public function shouldReturnFalseAndHaveNoLoginIfHeaderNotSet()
    {
        $mockLogin = Mockery::mock(Auth_Login::class);
        $headers = [
            'header-key' => 'header-value'
        ];

        $this->mockSessionRestorer
            ->shouldReceive('loginToBusinessTier')
            ->never();

        // Set this to show the object isn't retrieved by the helper
        Auth_Auth::setCurrentLogin($mockLogin);

        $result = $this->test->setSessionFromRequestHeaders($headers, 'some-other-string');

        $this->assertFalse($result);
        $this->assertEmpty($this->test->getLoginObject());
    }

    /**
     * @test
     * @throws Exception_MissingSessionIdException
     */
    public function shouldReturnTrueAndStoreLoginIfSessionIdSet()
    {
        $mockLogin = Mockery::mock(Auth_Login::class);
        $scriptUser = [
            'sessionID' => 'session-hash-value'
        ];

        $this->mockSessionRestorer
            ->shouldReceive('loginToBusinessTier')
            ->once()
            ->with('session-hash-value')
            ->andReturnTrue();
        Auth_Auth::setCurrentLogin($mockLogin);

        $result = $this->test->setSessionFromUserDetailsArray($scriptUser);

        $this->assertTrue($result);
        $this->assertSame($mockLogin, $this->test->getLoginObject());
    }

    /**
     * @test
     * @throws Exception_MissingSessionIdException
     */
    public function shouldThrowMissingSessionIdExceptionIfSessionIdNotSet()
    {
        $this->setExpectedException('Exception_MissingSessionIdException', 'Session ID not set');

        $mockLogin = Mockery::mock(Auth_Login::class);
        $scriptUser = [];

        $this->mockSessionRestorer
            ->shouldReceive('loginToBusinessTier')
            ->never();

        $this->test->setSessionFromUserDetailsArray($scriptUser);
    }
}
