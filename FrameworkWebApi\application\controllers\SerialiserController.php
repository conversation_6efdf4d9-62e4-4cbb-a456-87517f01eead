<?php
/**
 * SerialiserController Class File
 *
 * @package   Api
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @link      http://www.plus.net
 */
include_once '/local/codebase2005/modules/FrameworkWebApi/library/SerialiserBase.php';
include_once '/local/codebase2005/modules/FrameworkWebApi/library/EncodingHelper.php';

/**
 * SerialiserController Class
 * Provides read-only access to live appointing.
 *
 * @package   Api
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2011 Plusnet
 * @link      http://www.plus.net
 */
class SerialiserController extends Zend_Controller_Action
{
    /**
     * Runs before any action.  Here, we add the json context
     *
     * @return void
     */
    public function init()
    {
        $contextSwitch = $this->_helper->getHelper('contextSwitch');
        $contextSwitch->addActionContext('serialise', 'json')->initContext();
        $contextSwitch->addActionContext('deserialise', 'json')->initContext();
    }

    /**
     * serialise object
     *
     * http://<domain>/api/serialiser/serialise
     *
     * @api
     * @return void
     */
    public function serialiseAction()
    {
        $request = $this->getRequest();
        $anonymousObject = null;
        $rawBody = $request->getRawBody();

        // Use safe JSON decode for PHP 8.2 compatibility
        if (PHP_VERSION_ID >= 80200 && class_exists('EncodingHelper')) {
            $requestObject = EncodingHelper::safeJsonDecode($rawBody);
        } else {
            $requestObject = json_decode($rawBody, true);
        }

        $this->view->serialisedData = '';
        if ($requestObject !== null) {
            if (isset($requestObject['objectName']) && isset($requestObject['objectData'])) {
                $className = $requestObject['objectName'];
                $classData = $requestObject['objectData'];
                $anonymousObject = SerialiserBase::getSerialisedObject($className, $classData);
                if ($anonymousObject !== null) {
                    $this->view->serialisedData = $anonymousObject;
                }
            }
        }
    }

    /**
     * deserialise object
     *
     * http://<domain>/api/serialiser/deserialise
     *
     * @api
     * @return void
     */
    public function deserialiseAction()
    {
        $request = $this->getRequest();
        $rawBody = $request->getRawBody();

        // Use safe JSON decode for PHP 8.2 compatibility
        if (PHP_VERSION_ID >= 80200 && class_exists('EncodingHelper')) {
            $requestObject = EncodingHelper::safeJsonDecode($rawBody);
        } else {
            $requestObject = json_decode($rawBody, true);
        }

        $this->view->objectData = '';
        if ($requestObject !== null) {
            if (isset($requestObject['serialisedObjectData'])) {
                $jsonString = SerialiserBase::getEncodedObjectAsJson($requestObject['serialisedObjectData']);
                $this->view->objectData = base64_encode($jsonString);
            }
        }
    }

}
