<?php
/**
 * DirectDebitVerificationWebFormEntityTest
 *
 * @uses       PHPUnit_Framework_TestCase
 * @package    DirectDebitVerification
 * @subpackage Tests
 * <AUTHOR> Rollings <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Test\Model;

use Plusnet\DirectDebitVerification\Exceptions\DirectDebitVerificationInvalidArgumentException;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationAdditionalData;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationRequestData;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationWebFormEntity;

class DirectDebitVerificationWebFormEntityTest extends \PHPUnit_Framework_TestCase
{
    /**
     * Reset between runs
     *
     * @return void
     */
    public function tearDown()
    {
        \Db_Manager::rollback();
        \Db_Manager::restoreAdaptor("Auth");
    }

    /**
     * Tests that class default values are valid and unchanged
     *
     * @return void
     */
    public function testDefaultClassVariablesAreValid()
    {
        $hidden = DirectDebitVerificationWebFormEntity::DISPLAY_OPTION_HIDDEN;

        $expectedValidVariables = array(
            'ddPlanReference'            => 'DIRECTDEBIT',
            'ddPlanAltReference'         => 'DIRECTDEBIT',
            'legalEntityName'            => DirectDebitVerificationWebFormEntity::LEGAL_ENTITIES['default'],
            'showTitle'                  => $hidden,
            'showDob'                    => $hidden,
            'showEmail'                  => $hidden,
            'showMobile'                 => $hidden,
            'showDdPlanFields'           => $hidden,
            'showCompanyName'            => $hidden,
            'showApplyingAsCompanyCheck' => $hidden,
            'showDdPlanAltReference'     => DirectDebitVerificationWebFormEntity::DISPLAY_OPTION_READ_ONLY,
            'showErrorDetail'            => 'false',
        );

        $ddvWebFormEntity = $this->getMockDdvWebFormEntity(['checkIfValid']);
        $ddvWebFormEntity
            ->expects($this->once())
            ->method('checkIfValid')
            ->will($this->returnValue(true));

        $httpQueryString = $ddvWebFormEntity->getHttpQueryString();
        $this->assertContains(
            http_build_query($expectedValidVariables),
            $httpQueryString,
            '! Outputting class variables appear to have been modified, please check this is intentional, and' .
            'that values are valid. If so, update this test !'
        );
    }

    /**
     * Tests getTimestamp() - ensures returns are as expected
     *
     * @return void
     */
    public function testGetTimestamp()
    {
        $ddvWebFormEntity = $this->getMockDdvWebFormEntity();

        $now = (new \DateTime)->getTimestamp();
        $returnedTimestamp = $ddvWebFormEntity->getTimestamp();

        $this->assertInternalType('integer', $returnedTimestamp, 'Did not return a integer');
        $this->assertGreaterThanOrEqual(
            $now,
            $returnedTimestamp,
            'getTimestamp() response appears to be too far behind current unix timestamp: ' . $now
        );
        $this->assertLessThanOrEqual(
            $now + 5,
            $returnedTimestamp,
            'getTimestamp() response appears to be too far ahead of current unix timestamp: ' . $now
        );
    }

    /**
     * Tests state of showErrorDetail is not set to true in the codebase
     *
     * @return void
     */
    public function testShowErrorDetailIsNotTrue()
    {
        $ddvWebFormEntity = $this->getMockDdvWebFormEntity();
        $this->assertEquals(
            'false',
            $ddvWebFormEntity->showErrorDetail,
            'The showErrorDetails value should not be set to true in a Production environment!'
        );
    }

    /**
     * Get a DDV Additional Data object
     *
     * @param string $validationHash  A validation hash
     * @param array  $extraParameters Values to pass to constructor
     *
     * @return DirectDebitVerificationAdditionalData
     */
    private function getDdvAdditionalData($validationHash = 'abc123', array $extraParameters = [])
    {
        return new DirectDebitVerificationAdditionalData(
            $validationHash,
            $extraParameters
        );
    }

    /**
     * Helper to get a mock ddvWebFormHelper object
     *
     * @param array $methodsToReplaceWithTestDoubles Array containing method names you wish to be configurable
     *
     * @return DirectDebitVerificationWebFormEntity|\PHPUnit_Framework_MockObject_MockObject
     */
    private function getMockDdvWebFormEntity(
        $methodsToReplaceWithTestDoubles = null
    ) {
        return $this->getMock(
            DirectDebitVerificationWebFormEntity::class,
            $methodsToReplaceWithTestDoubles
        );
    }

    /**
     * Sets up an auth db adaptor that will be called if an exception is thrown and the logging functions called.
     *
     * @return void
     */
    private function setMockAuthAdaptor()
    {
        /** @var \Db_Adaptor | \PHPUnit_Framework_MockObject_MockObject $dbAdaptorAuthMock */
        $dbAdaptorAuthMock = $this->getMock(
            'Db_Adaptor',
            ['getScriptSessionDetails'],
            array('Auth', \Db_Manager::DEFAULT_TRANSACTION, true)
        );

        $dbAdaptorAuthMock->expects($this->any())
            ->method('getScriptSessionDetails')
            ->will($this->returnValue('U8GolZ00a5GGn+GFcu/HB/7+ch8='));

        \Db_Manager::setAdaptor('Auth', $dbAdaptorAuthMock, \Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * tests getHttpQueryString()
     *
     * @param array $requestDataArray    Request data to set
     * @param array $additionalDataArray Additional data to set
     * @param array $invalidValues       Values that should not pass validation, and hence be missing from output
     *
     * @dataProvider provideDataForTestGetHttpQueryString
     *
     * @return void
     */
    public function testGetHttpQueryString($requestDataArray, $additionalDataArray, $invalidValues = [])
    {
        $timestamp = 999;
        $ddPlanReference = 'DIRECTDEBIT';
        $validationHash = $additionalDataArray['validationHash'];

        $ddvWebFormEntity = new DirectDebitVerificationWebFormEntity();

        $expectedValues = [
            'ddPlanReference' => $ddPlanReference,
            'requestId'       => $validationHash,
            'requestUserId'   => $validationHash . $timestamp,
            'firstName'       => $requestDataArray['firstName'],
            'lastName'        => $requestDataArray['surname'],
            'companyName'     => $requestDataArray['companyName'],
            'currentPostCode' => $requestDataArray['bankAccountHolderAddress']['postCode'],
            'bankAccountName' => $requestDataArray['bankAccountName'],
            'sortCode'        => $requestDataArray['bankSortCode'],
            'accountNumber'   => $requestDataArray['bankAccountNumber'],
            'redirectUrl'     => $additionalDataArray['redirectUrl'],
            'callbackUrl'     => $additionalDataArray['callbackUrl'],
            'customData'      => $additionalDataArray['customData'],
            'legalEntityName' => $ddvWebFormEntity::LEGAL_ENTITIES['default'],
        ];

        if (in_array($requestDataArray['isp'], $ddvWebFormEntity::JLP_VISPS)) {
            $expectedValues['legalEntityName'] = $ddvWebFormEntity::LEGAL_ENTITIES['JohnLewisBB'];
            $expectedValues['contactPhone'] = $ddvWebFormEntity::SUPPORT_NUMBERS['JohnLewisBB'];
        }

        if ($requestDataArray['isBusiness'] === true && $requestDataArray['isp'] == 'plus.net') {
            $expectedValues['applyingAsCompany'] = 'true';
            $expectedValues['showCompanyName'] = $ddvWebFormEntity::DISPLAY_OPTION_VISIBLE;
            $expectedValues['showApplyingAsCompanyCheck'] = $ddvWebFormEntity::DISPLAY_OPTION_VISIBLE;
            $expectedValues['contactPhone'] = $ddvWebFormEntity::SUPPORT_NUMBERS['PlusnetBusiness'];
        }

        foreach ($invalidValues as $key) {
            unset($expectedValues[$key]);
        }

        $ddvRequestData = new DirectDebitVerificationRequestData();
        foreach ($requestDataArray as $key => $value) {
            $ddvRequestData->$key = $value;
        }

        $ddvAdditionalData = $this->getDdvAdditionalData($validationHash, $additionalDataArray);

        $mockDdvWebFormEntity = $this->getMockDdvWebFormEntity(['getTimestamp']);
        $mockDdvWebFormEntity
            ->expects($this->once())
            ->method('getTimestamp')
            ->will($this->returnValue($timestamp));

        $mockDdvWebFormEntity->hydrate($ddvRequestData, $ddvAdditionalData);
        $result = $mockDdvWebFormEntity->getHttpQueryString();

        foreach ($expectedValues as $property => $value) {
            if (in_array($property, $invalidValues)) {
                continue;
            }
            $expectedOutputString = http_build_query([$property => $value]);
            $this->assertContains(
                $expectedOutputString,
                $result,
                "Response does not contain expected string: '$expectedOutputString'!"
            );
        }

        foreach ($invalidValues as $index => $property) {
            $this->assertNotContains(
                $property,
                $result,
                "Response should not contain invalid string: '$property'!"
            );
        }
    }

    /**
     * Provides Data For TestGetHttpQueryString()
     *
     * @return array[]
     */
    public function provideDataForTestGetHttpQueryString()
    {
        $ddvWebFormEntity = new DirectDebitVerificationWebFormEntity();

        $validRequestDataArray = [
            'isp'                      => 'plus.net',
            'isBusiness'               => false,
            'firstName'                => 'Foo',
            'surname'                  => 'Bar',
            'companyName'              => 'My Company',
            'bankAccountHolderAddress' => ['postCode' => 'AB1 2CD'],
            'bankAccountName'          => 'FOO BAR JR',
            'bankSortCode'             => '123456',
            'bankAccountNumber'        => '********',
        ];

        $additionalDataArray = [
            'validationHash' => '123abc',
            'redirectUrl'    => 'https://my.redirectUrl.com',
            'callbackUrl'    => 'https://my.callbackurl.com',
            'customData'     => 'MyCustomData'
        ];

        $invalidRequestDataArray = [
            'firstName'                => 'abcdefghijklmnopqrstuvwxyz**********', // Too long - 36 chars
            'surname'                  => 'abcdefghijklmnopqrstuvwxyz**********', // Too long - 36 chars
            'companyName'              => '', // Empty string
            'bankAccountHolderAddress' => ['thoroughfare' => 'Foo Street'], // No 'postCode' element
            'bankAccountName'          => '!@£$%~^()', // Non-'BACS' characters
            'bankSortCode'             => '**********', // too long - 10
            'bankAccountNumber'        => '1234ACCT', // Not just a number
        ];

        $jlpArray = $validRequestDataArray;
        $jlpArray['isp'] = $ddvWebFormEntity::JLP_VISPS[0];

        $bizArray = $validRequestDataArray;
        $bizArray['isBusiness'] = true;

        $jlpAndBizArray = $validRequestDataArray;
        $jlpAndBizArray['isp'] = $ddvWebFormEntity::JLP_VISPS[0];
        $jlpAndBizArray['isBusiness'] = true;

        return [
            'shouldSetAllValidData'                   => [
                'requestDataArray'    => $validRequestDataArray,
                'additionalDataArray' => $additionalDataArray
            ],
            'shouldNotSetInvalidData'                 => [
                'requestDataArray'    => $invalidRequestDataArray,
                'additionalDataArray' => $additionalDataArray,
                'invalidValues'       => [
                    'firstName',
                    'lastName',
                    'companyName',
                    'currentPostCode',
                    'bankAccountName',
                    'sortCode',
                    'accountNumber',
                ]
            ],
            'shouldCustomiseForJlp'                   => [
                'requestDataArray'    => $jlpArray,
                'additionalDataArray' => $additionalDataArray
            ],
            'shouldCustomiseForPlusnetBusiness'       => [
                'requestDataArray'    => $bizArray,
                'additionalDataArray' => $additionalDataArray
            ],
            'shouldNotCustomiseForNonPlusnetBusiness' => [
                'requestDataArray'    => $jlpAndBizArray,
                'additionalDataArray' => $additionalDataArray
            ],
        ];
    }

    /**
     * Test getHttpQueryString() doesn't return values if hydrate() has not been called
     *
     * @return void
     */
    public function testGetHttpQueryStringThrowsExceptionIfHasNotBeenHydrated()
    {
        $regex = '/.*hydrate\(\).*/';
        $this->setMockAuthAdaptor();
        $webFormEntity = new DirectDebitVerificationWebFormEntity();
        $this->setExpectedExceptionRegExp(
            DirectDebitVerificationInvalidArgumentException::class,
            $regex
        );

        $webFormEntity->getHttpQueryString();
    }

    /**
     * Tests that values which *must* be present in the output to web forms cause an exception to be thrown if
     * they're not valid and present
     *
     * @return void
     */
    public function testGetHttpQueryStringThrowsExceptionIfInvalidMandatoryKeyValues()
    {
        $this->setMockAuthAdaptor();
        $webFormEntity = new DirectDebitVerificationWebFormEntity();
        $ddvRequestData = new DirectDebitVerificationRequestData();

        $additionalDataArray = [
            'validationHash' => '123abc',
            'redirectUrl'    => '',
            'callbackUrl'    => 'https://my.callbackurl.com',
            'customData'     => 'MyCustomData'
        ];
        $ddvAdditionalData = $this->getDdvAdditionalData($additionalDataArray['validationHash'], $additionalDataArray);

        $webFormEntity->hydrate($ddvRequestData, $ddvAdditionalData);
        $this->setExpectedExceptionRegExp(
            DirectDebitVerificationInvalidArgumentException::class,
            '/which fails validation rules/'
        );
        $webFormEntity->getHttpQueryString();
    }
}
