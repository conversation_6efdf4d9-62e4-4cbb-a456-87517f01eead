<?php
/**
 * Tests Auth_Login
 *
 * @package Framework
 * @subpackage Auth
 *
 * @version $Id: Login.test.php,v 1.5 2008-05-28 11:18:33 smarek Exp $
 *
 * @copyright 2006 PlusNet plc
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @since 23/03/2006
 */
class testLogin extends PHPUnit_Framework_TestCase
{
    private $_objSession;

    public function setUp()
    {
        try {
            $this->_objSession = $this->getMock('BusTier_Session', array(), array(), '', false);
            $objSessionClient = $this->getMock('BusTier_Client', array('getExternalUserID'));
            $objSessionClient->expects($this->any())
                             ->method('getExternalUserID')
                             ->will($this->returnValue(2));

            BusTier_BusTier::setClient('session', $objSessionClient);
        } catch (Exception $objException) {
            $this->fail('Failed to setup test data - ' . $objException->getMessage());
        }

    } // public function Setup()

    public function tearDown()
    {
        BusTier_BusTier::reset();
        Db_Manager::restoreConnection('coredb_master');
        Db_Manager::restoreAdaptor('Auth');
    }

    public function testConstructor()
    {
        try {
            $objLogin = new Auth_Login(new Auth_AuthSettings());
            $this->assertTrue(is_object($objLogin));
            $this->assertTrue($objLogin instanceof Auth_Login);
        } catch (Exception $objException) {
            $this->fail('Failed to create object with valid parameters - ' . $objException->getMessage());
        }

    } // public function testConstructor()


    public function testLoginID()
    {
        $objLogin = new Auth_Login(new Auth_AuthSettings());

        $this->assertEquals(2, $objLogin->getUserID());
    } // public function testLoginID()

    /**
     * Tests that hasBusinessLayerSessionExpired returns
     * false when logged in as valid user (not the default 'site user')
     * and an attempt to refresh the session succeeds.
     *
     * @return void
     **/
    public function testHasBusinessLayerSessionExpiredReturnsFalseWhenSessionHasNotExpired()
    {
        $this->assertFalse($this->callHasBusinessLayerSessionExpired(
            ['isSiteUser' => false, 'refreshBusinessTierSession' => true, 'hasLoggedOut' => false]
        ));
    }

    /**
     * Tests that hasBusinessLayerSessionExpired returns
     * false when logged in as valid user (not the default 'site user')
     * and an attempt to refresh the session succeeds, and the user is currently in the logging out process
     *
     * @return void
     **/
    public function testHasBusinessLayerSessionExpiredReturnsFalseWhenUserIsLoggingOut()
    {
        $this->assertFalse($this->callHasBusinessLayerSessionExpired(
            ['isSiteUser' => false, 'refreshBusinessTierSession' => true, 'hasLoggedOut' => true]
        ));
    }

    /**
     * Tests that hasBusinessLayerSessionExpired returns
     * true when an attempt to refresh the session fails.
     *
     * @return void
     **/
    public function testHasBusinessLayerSessionExpiredReturnsTrueWhenSessionHasExpired()
    {
        $this->assertTrue($this->callHasBusinessLayerSessionExpired(
            ['isSiteUser' => false, 'refreshBusinessTierSession' => false , 'hasLoggedOut' => false]
        ));
    }

    /**
     * Tests that hasBusinessLayerSessionExpired returns
     * false when not logged in (i.e. when using unauthenticated default 'site user')
     * and user is not in the logging out process.
     *
     * @return void
     **/
    public function testHasBusinessLayerSessionExpiredReturnsFalseWhenSiteUser()
    {
        $this->assertFalse($this->callHasBusinessLayerSessionExpired(
            ['isSiteUser' => true, 'refreshBusinessTierSession' => true, 'hasLoggedOut' => false]
        ));
    }

    /**
     * Tests that hasBusinessLayerSessionExpired returns
     * false when not logged in (i.e. when using unauthenticated default 'site user')
     * and user is in the logging out process.
     *
     * @return void
     **/
    public function testHasBusinessLayerSessionExpiredReturnsTrueWhenSiteUserAndUserHasLoggedOut()
    {
        $this->assertFalse($this->callHasBusinessLayerSessionExpired(
            ['isSiteUser' => true, 'refreshBusinessTierSession' => true, 'hasLoggedOut' => true]
        ));
    }

    public function callHasBusinessLayerSessionExpired($returnVals)
    {
        $objAuthSettings = new Auth_AuthSettings();

        $login = $this->getMock(
            'Auth_Login',
            array('isSiteUser', 'hasLoggedOut'),
            array($objAuthSettings)
        );

        $login
            ->method('isSiteUser')
            ->will($this->returnValue($returnVals['isSiteUser']));

        $login
            ->method('hasLoggedOut')
            ->will($this->returnValue($returnVals['hasLoggedOut']));

        $objBusTierSession = $this->getMock(
            'BusTier_Session',
            array('getSoapClientSession'),
            array('', '', '', new Auth_Session())
        );

        $objBusTierSession
            ->method('getSoapClientSession')
            ->will($this->returnValue(new Auth_Session()));

        $login->setBusTierSession($objBusTierSession);

        $objSessionManager = $this->getMock(
            'Auth_SessionManager',
            array('refreshBusinessTierSession')
        );

        // Test that an attempt is made to refresh framework session when logged in as a valid user
        if ($returnVals['hasLoggedOut']) {
            $objSessionManager
                ->expects($this->never())
                ->method('refreshBusinessTierSession');
        } else {
            $objSessionManager
                ->expects($this->once())
                ->method('refreshBusinessTierSession')
                ->will($this->returnValue($returnVals['refreshBusinessTierSession']));
        }

        $objAuth = $this->getMock(
            'Auth_Auth',
            array('setCommonSessionCookie')
        );

        // Test that legacy session is refreshed inline with framework session
        if (!$returnVals['hasLoggedOut'] && !$returnVals['isSiteUser'] && $returnVals['refreshBusinessTierSession']) {
            $objAuth
                ->expects($this->once())
                ->method('setCommonSessionCookie')
                ->will($this->returnValue(true));
        } else {
            $objAuth
                ->expects($this->never())
                ->method('setCommonSessionCookie');
        }

        return $login->hasBusinessLayerSessionExpired($objSessionManager, $objAuth);
    }

} // class testLogin extends PHPUnit_Framework_TestCase
