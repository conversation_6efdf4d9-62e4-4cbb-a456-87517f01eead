<?php
/**
 * Class Controller
 *
 * <AUTHOR> <mm<PERSON><PERSON><PERSON><PERSON>@plus.net>
 */

namespace Plusnet\DirectDebitVerification\CallbackReceiver;

use Plusnet\DirectDebitVerification\CallbackReceiver\Page;

class Controller extends \Mvc_PageController
{
    protected $arrPageMap = array(
        'Plusnet\DirectDebitVerification\CallbackReceiver\Page' => ''
    );

    protected $arrViewMap = array(
        'Plusnet\DirectDebitVerification\CallbackReceiver\Page' => array(
            'show' => array(
                \Mvc_ActionResult::SUCCESS => 'default'
            ),
        ),
    );
}
