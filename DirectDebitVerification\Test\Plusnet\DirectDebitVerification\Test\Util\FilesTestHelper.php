<?php
/**
 * Class FilesTestHelper
 *
 * <AUTHOR> <mm<PERSON><PERSON><PERSON><PERSON>@plus.net>
 */

namespace Plusnet\DirectDebitVerification\Test\Util;

use org\bovigo\vfs\vfsStream;

/**
 * Class FilesTestHelper
 *
 * <AUTHOR> <mm<PERSON><PERSON><PERSON><PERSON>@plus.net>
 */
class FilesTestHelper
{
    const BASE_DIR = 'root';

    private static $stubbedPath;

    public static $structure = array(
        'secrets' => array(),
        'config' => array()
    );

    /**
     * Prepare file structure
     *
     * @return \bovigo\vfs\vfsDirectory|\org\bovigo\vfs\vfsStreamDirectory
     */
    public static function prepareFileStructure()
    {
        return vfsStream::setup(self::BASE_DIR, null, self::$structure);
    }
}
