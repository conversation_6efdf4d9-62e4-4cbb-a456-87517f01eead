<?php
/**
 * DirectDebitVerificationWebFormEntity class
 *
 * Representation of relevant parameters and values from https://docs.pt-x.com/docs/direct-debit-application-form
 *
 * @package DirectDebitVerification
 * <AUTHOR> <<EMAIL>>
 */

/**
 * DirectDebitVerificationWebFormEntity class
 *
 * Representation of relevant parameters and values from https://docs.pt-x.com/docs/direct-debit-online-form
 *
 *   If adding a new output field to this class:
 *   1) Add new class variable matching the WebForm parameter you are trying to set (can use camelCase)
 *   2) Add an entry to the VALIDATION_MAP for the new variable so we can apply rules to ensure any values are valid
 *      N.B. Any empty or non validatable default values will NOT added to the outputted httpQueryString from this class
 *   3) Add a new setter method for the variable, if needed (see below)
 *   4) Add the name of the setter method to one of the hydration maps
 *      - If the variable name on the input object and in this class match when lower-cased, then you can instead use
 *        the value 'directIfValid' here and have the hydrateFromDataObjectUsingMap() method set the variable directly,
 *        (if the intended value is valid) without a superfluous setter method needed, or relying on less-traceable, and
 *        less prescriptive magic methods like __set().
 *      - Which hydration map to add an entry to is decided by which object the source information needed by the setter
 *        comes from: either a DirectDebitVerificationAdditionalData or a DirectDebitVerificationRequestData object
 *
 * @package DirectDebitVerification
 * <AUTHOR> Rollings <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Model;

use Plusnet\DirectDebitVerification\Exceptions\DirectDebitVerificationInvalidArgumentException;

class DirectDebitVerificationWebFormEntity
{
    const DISPLAY_OPTION_HIDDEN = 'hidden';
    const DISPLAY_OPTION_VISIBLE = 'visible';
    const DISPLAY_OPTION_READ_ONLY = 'readonly';

    const VALID_DISPLAY_OPTIONS = [
        self::DISPLAY_OPTION_HIDDEN,
        self::DISPLAY_OPTION_VISIBLE,
        self::DISPLAY_OPTION_READ_ONLY
    ];

    /**
     * Array of legal entities to use when referring to company requesting DD (shows in Direct Debit Guarantee text)
     */
    const LEGAL_ENTITIES = [
        'default'     => 'PLUSNET PLC',
        'Plusnet'     => 'PLUSNET PLC',
        'JohnLewisBB' => 'John Lewis Broadband',
    ];

    /**
     * Array of support contact numbers to override default one set on hosted web form via its config
     */
    const SUPPORT_NUMBERS = [
        'PlusnetBusiness' => '0800 028 0282',
        'JohnLewisBB'     => '0800 022 3300',
    ];

    /**
     * List of JohnLewis visps to allow JLP-specific customisations
     */
    const JLP_VISPS = array('johnlewis', 'greenbee', 'waitrose');

    /**
     * Values/properties which must be present, set and valid in the class to give a valid output
     *
     * @var string[]
     */
    const REQUIRED_OUTPUT_VALUES = [
        'ddPlanReference',
        'ddPlanAltReference',
        'requestId',
        'requestUserId',
        'redirectUrl',
        'callbackUrl',
    ];

    /**
     * Hydration map of DirectDebitVerificationRequestData object variables to setter methods
     *
     * @var array
     */
    const REQUEST_DATA_HYDRATION_MAP = [
        'firstName'                => 'directIfValid',
        'surname'                  => 'setLastNameIfValid',
        'isp'                      => 'setIspValuesIfRequired',
        'isBusiness'               => 'setIsBusinessValuesIfRequired',
        'companyName'              => 'directIfValid',
        'bankAccountHolderAddress' => 'setCurrentPostCodeIfValid',
        'bankAccountName'          => 'directIfValid',
        'bankSortCode'             => 'setSortCodeIfValid',
        'bankAccountNumber'        => 'setAccountNumberIfValid',
    ];

    /**
     * Hydration map of DirectDebitVerificationAdditionalData object variables to setter methods
     *
     * @var array
     */
    const ADDITIONAL_DATA_HYDRATION_MAP = [
        'validationHash' => 'setRequestIdAndRequestUserId',
        'redirectUrl'    => 'directIfValid',
        'callbackUrl'    => 'directIfValid',
        'customData'     => 'directIfValid'
    ];

    /**
     * Map of class variables to validation rules
     *
     * @var array
     */
    const VALIDATION_MAP = [
        'ddPlanReference'            => ['valStrLen' => 'between|6|18', 'valCustomRule' => 'isBACS'],
        'ddPlanAltReference'         => ['valStrLen' => 'between|6|18', 'valCustomRule' => 'isBACS'],
        'requestId'                  => ['valStrLen' => 'max|36'],
        'requestUserId'              => ['valStrLen' => 'max|255'],
        'firstName'                  => ['valStrLen' => 'max|32'],
        'lastName'                   => ['valStrLen' => 'max|32'],
        'applyingAsCompany'          => ['valCustomRule' => 'isBooleanString'],
        'companyName'                => ['valStrLen' => 'max|59'],
        'currentPostCode'            => ['valStrLen' => 'max|255'],
        'bankAccountName'            => ['valStrLen' => 'between|3|18', 'valCustomRule' => 'isBACS'],
        'sortCode'                   => ['valStrLen' => 'equals|6', 'valCustomRule' => 'isNumeric'],
        'accountNumber'              => ['valStrLen' => 'equals|8', 'valCustomRule' => 'isNumeric'],
        'redirectUrl'                => ['valStrLen' => 'max|2048'],
        'callbackUrl'                => ['valStrLen' => 'max|2048'],
        'customData'                 => ['valStrLen' => 'max|1024'],
        'legalEntityName'            => ['valStrLen' => 'between|2|64'],
        'contactPhone'               => ['valStrLen' => 'max|20'],
        'contactEmail'               => ['valStrLen' => 'max|255'],
        'showTitle'                  => ['valCustomRule' => 'isValidDisplayOption'],
        'showDob'                    => ['valCustomRule' => 'isValidDisplayOption'],
        'showEmail'                  => ['valCustomRule' => 'isValidDisplayOption'],
        'showMobile'                 => ['valCustomRule' => 'isValidDisplayOption'],
        'showDdPlanFields'           => ['valCustomRule' => 'isValidDisplayOption'],
        'showCompanyName'            => ['valCustomRule' => 'isValidDisplayOption'],
        'showApplyingAsCompanyCheck' => ['valCustomRule' => 'isValidDisplayOption'],
        'showDdPlanAltReference'     => ['valCustomRule' => 'isValidDisplayOption'],
        'showErrorDetail'            => ['valCustomRule' => 'isBooleanString']
    ];

    /**
     * Holds a flag to identify if the object has been hydrated or not
     *
     * @var bool
     */
    protected $hydrated = false;

    // ##################################
    // # Data - Prefilled parameters
    // ##################################
    /**
     * The reference name for the Direct Debit - a reference required for the form to load, but this will be unused
     *
     * @var string
     */
    protected $ddPlanReference = 'DIRECTDEBIT';

    /**
     * The alternative reference name for the Direct Debit - a reference required for the form to load, but this will
     * be unused
     *
     * @var string
     */
    protected $ddPlanAltReference = 'DIRECTDEBIT';

    /**
     * A GUID for the individual request
     *
     * Bottomline will not load a form that has already been submitted with the same requestId/requestUserId combination
     *
     * @var string
     */
    protected $requestId = null;

    /**
     * Identifier for the the 'user' making request
     *
     * @var string
     */
    protected $requestUserId = null;

    /**
     * Customer's first / given name
     *
     * @var string
     */
    protected $firstName = null;

    /**
     * Customer's last / family name / surname
     *
     * @var string
     */
    protected $lastName = null;

    /**
     * Whether the user is applying for Direct Debit on behalf of a company
     *
     * @var string
     */
    protected $applyingAsCompany = null;

    /**
     * Name of the company if are verifying details on behalf of one
     *
     * @var string
     */
    protected $companyName = null;

    /**
     * Postcode of the address to use to find the address from
     *
     * @var string
     */
    protected $currentPostCode = null;

    /**
     * The name or organisation that the bank account is held on behalf of, the bank account owner
     *
     * @var string
     */
    protected $bankAccountName = null;

    /**
     * The sort code of the bank account, identifying the holding financial institution & branch
     *
     * @var string
     */
    protected $sortCode = null;

    /**
     * An identifying number for the bank account
     *
     * @var string
     */
    protected $accountNumber = null;

    /**
     * The URL to redirect the browser to upon successful submitting of the PTX Web Form.
     *
     * Should be a holding page that will poll for receipt of the results of Bottomline's validation, which will
     * be POSTed to $callbackUrl
     *
     * @var string
     */
    protected $redirectUrl = null;

    /**
     * The URL that Bottomline should POST the assessed results of validating a successfully submitted Web Form to
     *
     * @var string
     */
    protected $callbackUrl = null;

    /**
     * Holds custom data to pass into Web Forms (max length 1024 characters)
     *
     * @var string
     */
    protected $customData = null;

    // ##################################
    // # Customisation - Content: Override default, displayed form values / content
    // ##################################
    /**
     * Company name to show on form for who will collect the Direct Debit (ie what will show on Bank Statement)
     *
     * @var string
     */
    protected $legalEntityName = self::LEGAL_ENTITIES['default'];

    /**
     * Contact phone number for our customer services if customer has problems completing form
     *  (A default should be set on the config page for each form with the web form supplier)
     *
     * @var string
     */
    protected $contactPhone = null;

    /**
     * Contact email for our customer services if customer has problems completing form
     *
     * @var string
     */
    protected $contactEmail = null;

    // ##################################
    // # Customisation - Layout: Toggle visibility of elements of the Web Form
    // ##################################
    /**
     * Whether to show the salutation 'title' entry field that holds e.g. Mr, Mrs etc.
     *
     * @var string
     */
    protected $showTitle = self::DISPLAY_OPTION_HIDDEN;

    /**
     * Whether to show the Date of Birth entry field on the Web Form
     *
     * @var string
     */
    protected $showDob = self::DISPLAY_OPTION_HIDDEN;

    /**
     * Whether to show the email entry fields on the Web Form
     *
     * @var string
     */
    protected $showEmail = self::DISPLAY_OPTION_HIDDEN;

    /**
     * Whether to show the mobile telephone number entry field on the Web Form
     *
     * @var string
     */
    protected $showMobile = self::DISPLAY_OPTION_HIDDEN;

    /**
     * Whether to show the Direct Debit Payment plan entry fields on the Web Form
     *
     * @var string
     */
    protected $showDdPlanFields = self::DISPLAY_OPTION_HIDDEN;

    /**
     * Whether to show the company name entry field on the Web Form
     *
     * @var string
     */
    protected $showCompanyName = self::DISPLAY_OPTION_HIDDEN;

    /**
     * Toggles the visibility and interactivity of the checkbox for applying as company
     *
     * @var string
     */
    protected $showApplyingAsCompanyCheck = self::DISPLAY_OPTION_HIDDEN;

    /**
     * * Toggles the visibility and interactivity of the Alt. reference on the Web Form (agent version only)
     *
     * @var string
     */
    public $showDdPlanAltReference = self::DISPLAY_OPTION_READ_ONLY;

    /**
     * Debug option to show cause of errors at Bottomline PTX - Should NOT be 'true' in production!
     *
     * @var string
     */
    public $showErrorDetail = 'false';

    /**
     * Hydrating method for DirectDebitVerificationWebFormEntity from objects
     * N.B Invalid data property values will be silently ignored, and not set
     *
     * @param DirectDebitVerificationRequestData    $ddvRequestData    Request data
     * @param DirectDebitVerificationAdditionalData $ddvAdditionalData Extra data
     *
     * @return void
     */
    public function hydrate(
        DirectDebitVerificationRequestData $ddvRequestData,
        DirectDebitVerificationAdditionalData $ddvAdditionalData
    ) {
        $this->hydrateFromDataObjectUsingMap($ddvRequestData, self::REQUEST_DATA_HYDRATION_MAP);
        $this->hydrateFromDataObjectUsingMap($ddvAdditionalData, self::ADDITIONAL_DATA_HYDRATION_MAP);
        $this->hydrated = true;
    }

    /**
     * Returns a URL-encoded string representation of set, valid properties of this class
     *
     * @return string
     */
    public function getHttpQueryString()
    {
        $this->checkIfValid();

        $validParameters = [];
        // Iterate through each property/variable of this class
        foreach (get_object_vars($this) as $property => $value) {
            if ($this->isValueValidForProperty($value, $property)) {
                $validParameters[$property] = $value;
            }
        }
        return http_build_query($validParameters);
    }

    /**
     * Check to ensure data has been populated by calling hydrate(), and output that will be generated has valid
     * mandatory values
     *
     * @return void
     * @throws DirectDebitVerificationInvalidArgumentException
     *
     */
    public function checkIfValid()
    {
        if (!$this->hydrated) {
            throw new DirectDebitVerificationInvalidArgumentException(
                self::class . '::getHttpQueryString() has been called without calling the hydrate() method first'
            );
        }

        foreach (static::REQUIRED_OUTPUT_VALUES as $property) {
            if (!$this->isValueValidForProperty($this->$property, $property)) {
                throw new DirectDebitVerificationInvalidArgumentException(
                    sprintf(
                        'Value for variable "%1$s" is currently set to: "%2$s" which fails validation rules. ' .
                        'This ultimately needs to be set and valid in the WebForm request URL. Ensure this is set in ' .
                        'the source DirectDebitVerificationRequestData / DirectDebitVerificationAdditionalData object' .
                        ' and hydrate() has been called. (validationHash="%3$s")',
                        $property,
                        $this->$property,
                        $this->requestId
                    )
                );
            }
        }
    }

    /**
     * Setter for requestId and requestUserId values using $validationHash passed from DDV AdditionalData object
     *
     * @param string $validationHash Validation hash to use
     *
     * @return void
     */
    protected function setRequestIdAndRequestUserId($validationHash)
    {
        $this->requestId = $validationHash;
        $this->requestUserId = $validationHash . $this->getTimestamp();
    }

    /**
     * Setter for lastName
     *
     * @param string $value Value to set, if valid
     *
     * @return void
     */
    protected function setLastnameIfValid($value)
    {
        $this->setValueIfValidForProperty($value, 'lastName');
    }

    /**
     * Sets specific values for non-default legal entities
     *
     * @param string $isp Which vISP the request is for
     *
     * @return void
     */
    protected function setIspValuesIfRequired($isp)
    {
        if (in_array($isp, self::JLP_VISPS)) {
            $this->legalEntityName = self::LEGAL_ENTITIES['JohnLewisBB'];
            $this->contactPhone = self::SUPPORT_NUMBERS['JohnLewisBB'];
        }
    }

    /**
     * Setter for customisations to allow business / company checks in Web Forms, if required
     *
     * N.B. Only Plusnet have valid business accounts, so will not set if this is a non-Plusnet account
     *
     * @param bool $isBusiness Whether DDV request is for a Business account
     *
     * @return void
     */
    protected function setIsBusinessValuesIfRequired($isBusiness)
    {
        if ($isBusiness === true
            && $this->legalEntityName == self::LEGAL_ENTITIES['Plusnet']
        ) {
            $this->applyingAsCompany = 'true';
            $this->showCompanyName = self::DISPLAY_OPTION_VISIBLE;
            $this->showApplyingAsCompanyCheck = self::DISPLAY_OPTION_VISIBLE;
            $this->contactPhone = self::SUPPORT_NUMBERS['PlusnetBusiness'];
        }
    }

    /**
     * Setter for currentPostCode, using value of $bankAccountHolderAddress['postCode']
     *
     * Note that in order to drive end-user use of the hosted address finder which pulls from a pre-formatted database,
     * we have intentionally only implemented the passing of the postcode to the Web Form. Other address values are not
     * passed
     *
     * @param array $bankAccountHolderAddress Array containing address details
     *
     * @return void
     */
    protected function setCurrentPostCodeIfValid(array $bankAccountHolderAddress)
    {
        if (isset($bankAccountHolderAddress['postCode'])) {
            $this->setValueIfValidForProperty($bankAccountHolderAddress['postCode'], 'currentPostCode');
        }
    }

    /**
     * Setter for sortCode
     *
     * @param string $value Value to set, if valid
     *
     * @return void
     */
    protected function setSortCodeIfValid($value)
    {
        $this->setValueIfValidForProperty($value, 'sortCode');
    }

    /**
     * Setter for accountNumber
     *
     * @param string $value Value to set, if valid
     *
     * @return void
     */
    protected function setAccountNumberIfValid($value)
    {
        $this->setValueIfValidForProperty($value, 'accountNumber');
    }

    /**
     * Helper function to set properties / values for this class using data objects and hydration maps
     *
     * @param object $object An object containing accessible class variables that exist in map
     * @param array  $map    A hydration map of variables to setter methods
     *
     * @return void
     */
    protected function hydrateFromDataObjectUsingMap($object, array $map)
    {
        foreach ($map as $propertyName => $setter) {
            if ($setter == 'directIfValid') {
                $value = $object->$propertyName;
                $this->setValueIfValidForProperty($value, $propertyName);
                continue;
            }
            $this->$setter($object->$propertyName);
        }
    }

    /**
     * Wrapper to call isValueValidForProperty() and set property if value is valid
     *
     * @param string $value    The value to check
     * @param string $property The property to check against and set if valid
     *
     * @return void
     */
    protected function setValueIfValidForProperty($value, $property)
    {
        if (property_exists($this, $property) && $this->isValueValidForProperty($value, $property)) {
            $this->$property = $value;
        }
    }

    /**
     * Uses the validation map rules to determine if a value is valid for the selected property
     *
     * @param string $value    The value to check
     * @param string $property The property to check against
     *
     * @return bool
     */
    protected function isValueValidForProperty($value, $property)
    {
        if (empty($value) || !array_key_exists($property, self::VALIDATION_MAP)) {
            return false;
        }

        $validationRules = self::VALIDATION_MAP[$property];
        foreach ($validationRules as $validationMethod => $condition) {
            $validationCheck = $this->$validationMethod($condition, $value);
            if ($validationCheck === false) {
                return false;
            }
        }

        return true;
    }

    /**
     * Perform validations based on string length, as given in the rules string
     *
     * Examples:
     * - 'max|255'       - Checks that $value isn't greater than 255 chars
     * - 'equals|6'      - Checks that $value is exactly 6 chars in length
     * - 'between|1|100' - Checks that $value is between 1 and 100, inclusive
     *
     * @param string $rulesString A rule followed by arguments
     * @param string $value       Value to check with rule
     *
     * @return bool
     */
    protected function valStrLen($rulesString, $value)
    {
        $isValid = false;
        $rulesArray = explode("|", $rulesString);
        switch ($rulesArray[0]) {
            case 'max':
                $isValid = (strlen($value) <= $rulesArray[1]);
                break;
            case 'equals':
                $isValid = (strlen($value) == $rulesArray[1]);
                break;
            case 'between':
                $isValid = (strlen($value) >= $rulesArray[1] && strlen($value) <= $rulesArray[2]);
                break;
        }
        return $isValid;
    }

    /**
     * Perform validations based on custom rules
     *
     * @param string $rule  Name of rule to apply
     * @param string $value Value to validate against rule
     *
     * @return bool
     */
    protected function valCustomRule($rule, $value)
    {
        $isValid = false;
        switch ($rule) {
            case 'isBACS':
                // The BACS pattern doesn't allow lowercase, but the Web Forms will convert on the fly
                $value = strtoupper($value);
                $isValid = (bool)(preg_match('/^[A-Z \-.&\/]*$/', $value));
                break;
            case 'isNumeric':
                $isValid = (bool)(preg_match('/^\d*$/', $value));
                break;
            case 'isValidDisplayOption':
                $isValid = (in_array($value, self::VALID_DISPLAY_OPTIONS));
                break;
            case 'isBooleanString':
                $value = strtolower($value);
                $isValid = ($value === 'true' || $value === 'false');
                break;
        }
        return $isValid;
    }

    /**
     * Gets current DateTime timestamp
     *
     * @return int The current Unix timestamp
     */
    public function getTimestamp()
    {
        return (new \DateTime())->getTimestamp();
    }
}
