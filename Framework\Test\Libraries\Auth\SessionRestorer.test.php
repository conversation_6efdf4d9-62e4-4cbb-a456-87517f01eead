<?php
/**
 * <AUTHOR>
 */
class SessionRestorerTest extends PHPUnit_Framework_TestCase
{
    /**
     * @return void
     * @throws Exception
     */
    public function testSessionIsSetInTheBusinessTier()
    {
        $session = '12345';
        $authDetails = ['anything' => 'something'];

        $dbAdaptor = $this->createMock('Db_Adaptor', ['getAuthDetailsBySessionId']);
        $dbAdaptor->method('getAuthDetailsBySessionId')->willReturn($authDetails);

        $legacyCodebaseSession = $this->createMock('Auth_BusTierSession');
        $frameworkSession = $this->createMock('BusTier_Session');
        $authLogin = $this->createMock('Auth_Login', ['setLoginSession']);
        $authLogin->expects($this->once())->method('setLoginSession')->with($frameworkSession);

        $sessionManager = $this
            ->getMockBuilder('Auth_SessionRestorer')
            ->setConstructorArgs([$dbAdaptor])
            ->setMethods(['createLegacyBusTierSession', 'createFrameworkBusTierSession', 'createAuthLogin'])
            ->getMock();
        $sessionManager
            ->expects($this->once())
            ->method('createLegacyBusTierSession')
            ->with($authDetails)
            ->willReturn($legacyCodebaseSession);
        $sessionManager
            ->expects($this->once())
            ->method('createFrameworkBusTierSession')
            ->with($legacyCodebaseSession)
            ->willReturn($frameworkSession);
        $sessionManager
            ->expects($this->once())
            ->method('createAuthLogin')
            ->willReturn($authLogin);

        $result = $sessionManager->loginToBusinessTier($session);

        $this->assertTrue($result);
        $this->assertEquals($frameworkSession, BusTier_BusTier::getSession());
        $this->assertEquals($authLogin, Auth_Auth::getCurrentLogin());
    }

    /**
     * Creates a basic mock object
     *
     * @param string $class     the class to be mocked
     * @param array  $functions the functions to be mocked
     *
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    private function createMock($class, $functions = [])
    {
        return $this->getMockBuilder($class)->disableOriginalConstructor()->setMethods($functions)->getMock();
    }
}
