# DD-Verification-Service PHP 8.2 Compatibility Fix

## Problem Summary

You're experiencing a JSON encoding issue between:
- **dd-verification-service** (PHP 8.2) in LIVE environment
- **FrameworkWebApi** (PHP 5.6) 

The error is:
```
JsonParseException: Unexpected character ('�' (code 65533 / 0xfffd)): expected a valid value
```

## Root Cause

- **Test environment**: dd-verification-service runs on PHP 8.0 ✅ (works fine)
- **Live environment**: dd-verification-service runs on PHP 8.2 ❌ (fails with Unicode issues)

PHP 8.2 introduced changes in JSON encoding that create Unicode characters incompatible with PHP 5.6.

## Communication Flow

```
orders-api → pn-billing-api → FrameworkWebApi (PHP 5.6) → dd-verification-service (PHP 8.2) → FrameworkWebApi (PHP 5.6) → pn-billing-api
```

The issue occurs when dd-verification-service (PHP 8.2) generates JSON that FrameworkWebApi (PHP 5.6) cannot parse.

## Solution

### Step 1: Diagnose the Issue

Run the diagnostic script to understand your current environment:

```bash
./diagnose-dd-verification-issue.sh
```

**Before running**: Update the `CONTAINER_NAME` variable in the script to match your actual dd-verification-service container name.

### Step 2: Deploy the Fix

Run the deployment script to apply the PHP 8.2 compatibility fix:

```bash
./deploy-dd-verification-php82-fix.sh
```

**Before running**: Update the `CONTAINER_NAME` variable in the script to match your actual dd-verification-service container name.

### Step 3: Test the Fix

1. **Monitor logs** for the absence of Unicode character errors:
   ```bash
   docker logs -f <container-name> | grep -i "json\|unexpected\|character"
   ```

2. **Test a DirectDebit verification request** through the normal flow

3. **Check for success** in logs:
   ```bash
   docker logs -f <container-name> | grep -i "success\|stored.*database"
   ```

## What the Fix Does

The fix modifies the `DirectDebitData::toJson()` method in dd-verification-service to:

1. **Detect PHP 8.2** and use compatibility mode
2. **Clean problematic characters** (Unicode replacement characters, control characters)
3. **Use PHP 5.6 compatible JSON flags** only
4. **Convert Unicode to ASCII** where possible
5. **Add comprehensive logging** for debugging

## Files Modified

1. `DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php` - Core fix
2. `DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php` - Helper (new)
3. `DirectDebitVerification/test-json-compatibility.php` - Test script (new)

## Expected Results

After applying the fix:
- ✅ No more "Unexpected character ('�')" errors in pn-billing-api
- ✅ DirectDebit verification requests complete successfully
- ✅ Logs appear in dd-verification-service (where they should)
- ✅ JSON generated by PHP 8.2 is properly parsed by PHP 5.6

## Rollback Plan

If issues occur, rollback using the automatically created backup:

```bash
docker exec -it <container-name> cp /local/codebase2005/modules/DirectDebitVerification/backup-*/DirectDebitData.php.backup /local/codebase2005/modules/DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php
docker exec -it <container-name> kill -USR1 1
```

## Debug Mode

To enable detailed JSON processing logs:

```bash
docker exec -it <container-name> bash -c 'echo "export DDV_JSON_DEBUG=1" >> /etc/environment'
```

Then monitor with:
```bash
docker logs -f <container-name> | grep DDV-JSON
```

## Why This Explains Everything

- ✅ **No logs in dd-verification-service on LIVE**: Because PHP 8.2 was causing silent failures
- ✅ **Works in TEST**: Because TEST uses PHP 8.0, not PHP 8.2
- ✅ **JSON parsing errors in pn-billing-api**: Because FrameworkWebApi couldn't parse PHP 8.2 generated JSON
- ✅ **Character encoding issues**: Because PHP 8.2 has different JSON handling than PHP 8.0/5.6

## Support

If you encounter issues:

1. Run the diagnostic script first
2. Check the container logs for specific error messages
3. Verify the container name is correct in the scripts
4. Ensure the dd-verification-service container is running

The fix addresses the exact issue you described: PHP 8.2 compatibility problems that don't exist in PHP 8.0 (test environment).
