<?php
/**
 * Class to hold methods common to Auth_Filter tests
 *
 * @package    Framework
 * @subpackage Auth
 * <AUTHOR> <<EMAIL>>
 * @since      October 2014
 */

/**
 * Filter_TestCase Class
 *
 * @package    Framework
 * @subpackage Auth
 * <AUTHOR> <<EMAIL>>
 * @since      October 2014
 */
abstract class Filter_TestCase extends Plusnet_TestCase
{
    /**
     * Mocks Auth_Login object
     *
     * @return PHPUnit_Framework_MockObject_MockObject
     */
    protected function createMockLoginWithChildAccounts()
    {
        $childLogins = array(
            1 => $this->createFullyMockedObject('Auth_Login'),
            2 => $this->createFullyMockedObject('Auth_Login')
        );
        $mockLogin = $this->createFullyMockedObject(
            'Auth_Login',
            array(
                'getUserID' => array(1, '123abc'),
                'getChildAccounts' => array(1, $childLogins)
            )
        );

        return $mockLogin;
    }
}
