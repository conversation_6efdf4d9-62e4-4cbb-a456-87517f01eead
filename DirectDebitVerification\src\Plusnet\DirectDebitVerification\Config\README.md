## DirectDebitVerification: Environmental Config
This folder should hold a JSON file (`directDebitVerificationConfig.json`) containing the appropriate environment/production configuration for this repository to allow portability, including validation API URLs, validation score levels and security keys.

This will __not__ be stored inside this repo, but be deployed via a separate mechanism when this repo is deployed.

See `Plusnet\DirectDebitVerification\Model\DirectDebitVerificationConfig` for more details on the expected format of this file, since this class handles loading and parsing of the file.

Example json files for the development environment can be located in the equivalent 'Config' folder in the Test section of this repo.