<?php
/**
 * DirectDebitVerificationConfigHelperTest
 *
 * @uses       PHPUnit_Framework_TestCase
 * @package    DirectDebitVerification
 * @subpackage Tests
 * <AUTHOR> Rollings <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Test\Helpers;

use Plusnet\DirectDebitVerification\Helpers\DirectDebitVerificationConfigHelper;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationConfig;
use Plusnet\DirectDebitVerification\Test\Model\TestConfig;

class DirectDebitVerificationConfigHelperTest extends \PHPUnit_Framework_TestCase
{

    /**
     * Set up application state
     *
     * @return void
     */
    public function setUp()
    {
        DirectDebitVerificationConfigHelper::reset();
    }

    /**
     * Reset application state
     *
     * @return void
     */
    public function tearDown()
    {
        DirectDebitVerificationConfigHelper::reset();
    }

    /**
     * Tests the get() method
     *
     * @return void
     */
    public function testGet()
    {
        $ddvConfig = $this->callConfigHelperGetWithTestConfigFile();

        $this->assertInstanceOf(
            DirectDebitVerificationConfig::class,
            $ddvConfig,
            'Not returning instance of DirectDebitVerificationConfig!'
        );
    }

    /**
     * Tests that config is cached
     *
     * @return void
     */
    public function testGetEnsureCachingWorks()
    {
        $this->callConfigHelperGetWithTestConfigFile();
        $initialTimestamp = $this->getLastCached();
        sleep(1); // Wait to ensure timestamp would be higher
        $this->callConfigHelperGetWithTestConfigFile();
        $this->assertEquals($initialTimestamp, $this->getLastCached(), 'Config is not being cached');
    }

    /**
     * Tests setObject() method
     *
     * @return void
     */
    public function testSetObject()
    {
        $expectedResponse = 'myTestUrl';
        $mockConfig = $this->getMock(
            DirectDebitVerificationConfig::class,
            ['getWebFormProviderBaseURI']
        );
        $mockConfig->expects($this->once())
            ->method('getWebFormProviderBaseURI')
            ->will($this->returnValue($expectedResponse));

        DirectDebitVerificationConfigHelper::setObject($mockConfig);
        $ddvConfig = DirectDebitVerificationConfigHelper::get();
        $this->assertEquals(
            $expectedResponse,
            $ddvConfig->getWebFormProviderBaseURI(),
            'Not returning stored config object, because test double response was not received!'
        );
        $this->assertNotNull($this->getLastCached(), '$lastCached was not updated when setObject() was called');
    }

    /**
     * tests getLastCached()
     *
     * @return void
     */
    public function testGetLastCached()
    {
        $lastCached = DirectDebitVerificationConfigHelper::getLastCached();
        $this->assertNull($lastCached, 'Newly-reset Helper Class does not have a null default $lastCached value');

        $startTime = new \DateTimeImmutable();
        $this->callConfigHelperGetWithTestConfigFile();
        $lastCached = $this->getLastCached();

        $this->assertGreaterThanOrEqual($startTime, $lastCached, 'Last cached time is in the future');
        $this->assertLessThanOrEqual(
            $startTime->modify("+2 sec"),
            $lastCached,
            'Last cached time is too far in the past!'
        );
    }

    /**
     * Tests the shouldRefreshCache() method
     *
     * @return void
     */
    public function testShouldRefreshCache()
    {
        $this->assertTrue(
            $this->callShouldRefreshCache(),
            'Should refresh cache if no value for $getLastCached'
        );

        $this->setLastCached();
        $this->assertFalse(
            $this->callShouldRefreshCache(''),
            'Shouldn\'t refresh cache if $getLastCached is set, empty string filepath passed in and cache not expired'
        );

        $this->setLastCached();
        $this->assertTrue(
            $this->callShouldRefreshCache('foo'),
            'Should refresh cache if value for $getLastCached, and new filepath passed in'
        );

        $this->setLastCached();
        $this->assertFalse(
            $this->callShouldRefreshCache(),
            'Should NOT refresh cache if it has not expired'
        );

        $expiringTimePeriodPlusOne =
            DirectDebitVerificationConfigHelper::CACHE_TIME_SECONDS + 1;
        $expiredCachedTime = new \DateTimeImmutable('-' . $expiringTimePeriodPlusOne . ' seconds');
        $this->setLastCached($expiredCachedTime);
        $this->assertTrue(
            $this->callShouldRefreshCache(),
            'Should refresh cache if it has expired'
        );
    }

    /**
     * Helper function to call get() with the test config json file
     *
     * @param $overrideFilePath Path to override
     * @return bool
     */
    private function callShouldRefreshCache($overrideFilePath = '')
    {
        return DirectDebitVerificationConfigHelper::shouldRefreshCache($overrideFilePath);
    }

    /**
     * Helper function to call get() with the test config json file
     *
     * @return DirectDebitVerificationConfig|null
     */
    private function callConfigHelperGetWithTestConfigFile()
    {
        $testConfigFile = $this->getLocalTestConfigFilePath();
        return DirectDebitVerificationConfigHelper::get($testConfigFile);
    }

    /**
     *  Helper function to call getLastCached() method
     *
     * @return \DateTimeImmutable|null
     */
    private function getLastCached()
    {
        return DirectDebitVerificationConfigHelper::getLastCached();
    }

    /**
     *  Helper function to call setLastCached() method
     *
     * @param \DateTimeImmutable|null $overrideDateTime A DateTime to override with
     *
     * @return void
     */
    private function setLastCached(\DateTimeImmutable $overrideDateTime = null)
    {
        DirectDebitVerificationConfigHelper::setLastCached($overrideDateTime);
    }

    /**
     * Helper function to get the path to the test config json file
     *
     * @return string
     */
    private function getLocalTestConfigFilePath()
    {
        return TestConfig::LOCAL_TEST_CONFIG_FILE;
    }
}
