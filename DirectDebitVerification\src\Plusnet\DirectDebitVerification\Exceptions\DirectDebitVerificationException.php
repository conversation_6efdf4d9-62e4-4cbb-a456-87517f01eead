<?php

/**
 * Generic DirectDebitVerification exception
 *
 * @package    DirectDebitVerification
 * @subpackage Exceptions
 * <AUTHOR> <marcin.<PERSON><PERSON><PERSON><PERSON><PERSON>@bt.com>
 */

namespace Plusnet\DirectDebitVerification\Exceptions;

/**
 * Class DirectDebitVerificationException
 *
 * @package    DirectDebitVerification
 * @subpackage Exceptions
 * <AUTHOR> <marcin.mi<PERSON><PERSON>@bt.com>
 */
class DirectDebitVerificationException extends DirectDebitVerificationBaseException
{
    /**
     * Returns the log level of this exception
     *
     * @return string Exception level constant from \Log_LogData::LOG_LEVEL_*
     */
    public function logLevel()
    {
        return \Log_LogData::LOG_LEVEL_ERROR;
    }
}
