<?php

namespace Plusnet\DirectDebitVerification\Exceptions;

/**
 * DirectDebitVerification InvalidConfig exception
 *
 * @package    DirectDebitVerification
 * @subpackage Exceptions
 * <AUTHOR> <<EMAIL>>
 */
class DirectDebitVerificationInvalidConfigException extends DirectDebitVerificationBaseException
{
    /**
     * Returns the log level of this exception
     *
     * @return string Exception level constant from Log_LogData::LOG_LEVEL_*
     */
    public function logLevel()
    {
        return \Log_LogData::LOG_LEVEL_ERROR;
    }
}
