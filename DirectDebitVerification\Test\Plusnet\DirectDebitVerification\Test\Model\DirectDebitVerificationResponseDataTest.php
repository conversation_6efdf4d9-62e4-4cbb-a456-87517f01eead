<?php
/**
 *
 *
 * <AUTHOR> <mm<PERSON><PERSON><PERSON><PERSON>@plus.net>
 */

namespace Plusnet\DirectDebitVerification\Test\Model;

use Plusnet\DirectDebitVerification\Exceptions\DirectDebitVerificationException;
use Plusnet\DirectDebitVerification\Helpers\AuditLogHelper;
use Plusnet\DirectDebitVerification\Helpers\DataEncryptionHelper;
use Plusnet\DirectDebitVerification\Helpers\DirectDebitVerificationConfigHelper;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationResponseData;

class DirectDebitVerificationResponseDataTest extends \PHPUnit_Framework_TestCase
{
    const DIRECT_DEBIT_VERIFICATION_TRANSACTION = 'DIRECT_DEBIT_VERIFICATION_TRANSACTION';

    private $validationHash = '8eb397eb4dbc8933d975e87e2b816af7';
    private $accountId = 2558172;
    private $id = 21;
    private $created = '2022-10-19';
    private $lastUpdated = '2022-10-19';
    private $stage = 'stage';
    private $company = 'Company name';
    private $verificationStatus = 'status';
    private $currentAddressRecomendedStatus = 'Current address recomended status';
    private $formId = 1;
    private $formTypeId = 2;
    private $formType = 'form type';
    private $title = 'Mr.';
    private $firstName = 'Topsy';
    private $middleName = '-';
    private $lastName = 'Turvy';
    private $dOB = '1950-01-01';
    private $email = '<EMAIL>';
    private $mobile = '**********';
    private $applyingAsCompany = false;
    private $companyName = null;
    private $currentHouseNameNumber = 1;
    private $currentStreet1 = 'Street';
    private $currentStreet2 = null;
    private $currentTown = 'Small Town';
    private $currentPostcode = 'ST1 2AB';
    private $currentCountry = 'Small Country';
    private $bankAccountName = 'Mr. Topsy-Turvy';
    private $sortCode = '112233';
    private $accountNumber = '********';
    private $ptxProfileName = 'PN';
    private $ddDebtorReference = null;
    private $ddPlanAltReference = 'Alt reference';
    private $ddPlanReference = 'Reference';
    private $ddPlanSpecification = 'Spec';
    private $ddNoOfCollections = 1;
    private $ddRegularAmount = 121;
    private $ddFirstAmount = 121;
    private $ddLastAmount = 121;
    private $ddStartDate = 2022 - 11 - 01;
    private $ddPlanEndByDate = null;
    private $planEndByDate = null;
    private $planEndType = null;
    private $customData = [];
    private $formName = 'DDV';
    private $giftAid = false;
    private $bankName = 'Small Bank';
    private $bankAccountCreated = null;
    private $companyRegistrationNumber = null;
    private $nameScore = 8;
    private $addressScore = 7;
    private $proprietorScore = null;
    private $registrationMatch = null;
    private $accountStatus = 1;
    private $namePassScore = 7;
    private $addressPassScore = 7;
    private $proprietorPassScore = null;
    private $registrationPassValue = null;
    private $companyNamePassScore = null;
    private $companyAddressPassScore = null;
    private $expirationTimer = null;
    private $verificationApplied = 'True';
    private $hasPassedValidation = true;

    /**
     * Get array representation
     *
     * @return array
     */
    private function getArrayRepresentation()
    {
        return [
            'ValidationHash' => $this->validationHash,
            'AccountId' => $this->accountId,
            'Id' => $this->id,
            'Created' => $this->created,
            'LastUpdated' => $this->lastUpdated,
            'Stage' => $this->stage,
            'Company' => $this->company,
            'VerificationStatus' => $this->verificationStatus,
            'CurrentAddressRecomendedStatus' => $this->currentAddressRecomendedStatus,
            'FormId' => $this->formId,
            'FormTypeId' => $this->formTypeId,
            'FormType' => $this->formType,
            'Title' => $this->title,
            'FirstName' => $this->firstName,
            'MiddleName' => $this->middleName,
            'LastName' => $this->lastName,
            'DOB' => $this->dOB,
            'Email' => $this->email,
            'Mobile' => $this->mobile,
            'ApplyingAsCompany' => $this->applyingAsCompany,
            'CompanyName' => $this->companyName,
            'CurrentHouseNameNumber' => $this->currentHouseNameNumber,
            'CurrentStreet1' => $this->currentStreet1,
            'CurrentStreet2' => $this->currentStreet2,
            'CurrentTown' => $this->currentTown,
            'CurrentPostcode' => $this->currentPostcode,
            'CurrentCountry' => $this->currentCountry,
            'BankAccountName' => $this->bankAccountName,
            'SortCode' => $this->sortCode,
            'AccountNumber' => $this->accountNumber,
            'PtxProfileName' => $this->ptxProfileName,
            'DdDebtorReference' => $this->ddDebtorReference,
            'DdPlanAltReference' => $this->ddPlanAltReference,
            'DdPlanReference' => $this->ddPlanReference,
            'DdPlanSpecification' => $this->ddPlanSpecification,
            'DdNoOfCollections' => $this->ddNoOfCollections,
            'DdRegularAmount' => $this->ddRegularAmount,
            'DdFirstAmount' => $this->ddFirstAmount,
            'DdLastAmount' => $this->ddLastAmount,
            'DdStartDate' => $this->ddStartDate,
            'DdPlanEndByDate' => $this->ddPlanEndByDate,
            'PlanEndByDate' => $this->planEndByDate,
            'PlanEndType' => $this->planEndType,
            'CustomData' => $this->customData,
            'FormName' => $this->formName,
            'GiftAid' => $this->giftAid,
            'BankName' => $this->bankName,
            'BankAccountCreated' => $this->bankAccountCreated,
            'CompanyRegistrationNumber' => $this->companyRegistrationNumber,
            'NameScore' => $this->nameScore,
            'AddressScore' => $this->addressScore,
            'ProprietorScore' => $this->proprietorScore,
            'RegistrationMatch' => $this->registrationMatch,
            'AccountStatus' => $this->accountStatus,
            'NamePassScore' => $this->namePassScore,
            'AddressPassScore' => $this->addressPassScore,
            'ProprietorPassScore' => $this->proprietorPassScore,
            'RegistrationPassValue' => $this->registrationPassValue,
            'CompanyNamePassScore' => $this->companyNamePassScore,
            'CompanyAddressPassScore' => $this->companyAddressPassScore,
            'ExpirationTimer' => $this->expirationTimer,
            'VerificationApplied' => $this->verificationApplied
        ];
    }

    /**
     * Set up before each run
     *
     * @return void
     */
    public function setUp()
    {
        $testConfigFile = TestConfig::LOCAL_TEST_CONFIG_FILE;
        DirectDebitVerificationConfigHelper::get($testConfigFile);
    }

    /**
     * Reset application state
     *
     * @return void
     */
    public function tearDown()
    {
        DirectDebitVerificationConfigHelper::reset();
    }

    /**
     * Test getters and setters works properly
     *
     * @return void
     */
    public function testGettersAndSettersWorksProperly()
    {
        $dDvResponseData = new DirectDebitVerificationResponseData();
        $dDvResponseData->setValidationHash($this->validationHash);
        $dDvResponseData->setAccountId($this->accountId);
        $dDvResponseData->setId($this->id);
        $dDvResponseData->setCreated($this->created);
        $dDvResponseData->setLastUpdated($this->lastUpdated);
        $dDvResponseData->setStage($this->stage);
        $dDvResponseData->setCompany($this->company);
        $dDvResponseData->setVerificationStatus($this->verificationStatus);
        $dDvResponseData->setCurrentAddressRecomendedStatus($this->currentAddressRecomendedStatus);
        $dDvResponseData->setFormId($this->formId);
        $dDvResponseData->setFormTypeId($this->formTypeId);
        $dDvResponseData->setFormType($this->formType);
        $dDvResponseData->setTitle($this->title);
        $dDvResponseData->setFirstName($this->firstName);
        $dDvResponseData->setMiddleName($this->middleName);
        $dDvResponseData->setLastName($this->lastName);
        $dDvResponseData->setDOB($this->dOB);
        $dDvResponseData->setEmail($this->email);
        $dDvResponseData->setMobile($this->mobile);
        $dDvResponseData->setApplyingAsCompany($this->applyingAsCompany);
        $dDvResponseData->setCompanyName($this->companyName);
        $dDvResponseData->setCurrentHouseNameNumber($this->currentHouseNameNumber);
        $dDvResponseData->setCurrentStreet1($this->currentStreet1);
        $dDvResponseData->setCurrentStreet2($this->currentStreet2);
        $dDvResponseData->setCurrentTown($this->currentTown);
        $dDvResponseData->setCurrentPostcode($this->currentPostcode);
        $dDvResponseData->setCurrentCountry($this->currentCountry);
        $dDvResponseData->setBankAccountName($this->bankAccountName);
        $dDvResponseData->setSortCode($this->sortCode);
        $dDvResponseData->setAccountNumber($this->accountNumber);
        $dDvResponseData->setPtxProfileName($this->ptxProfileName);
        $dDvResponseData->setDdDebtorReference($this->ddDebtorReference);
        $dDvResponseData->setDdPlanAltReference($this->ddPlanAltReference);
        $dDvResponseData->setDdPlanReference($this->ddPlanReference);
        $dDvResponseData->setDdPlanSpecification($this->ddPlanSpecification);
        $dDvResponseData->setDdNoOfCollections($this->ddNoOfCollections);
        $dDvResponseData->setDdRegularAmount($this->ddRegularAmount);
        $dDvResponseData->setDdFirstAmount($this->ddFirstAmount);
        $dDvResponseData->setDdLastAmount($this->ddLastAmount);
        $dDvResponseData->setDdStartDate($this->ddStartDate);
        $dDvResponseData->setDdPlanEndByDate($this->ddPlanEndByDate);
        $dDvResponseData->setPlanEndByDate($this->planEndByDate);
        $dDvResponseData->setPlanEndType($this->planEndType);
        $dDvResponseData->setCustomData($this->customData);
        $dDvResponseData->setFormName($this->formName);
        $dDvResponseData->setGiftAid($this->giftAid);
        $dDvResponseData->setBankName($this->bankName);
        $dDvResponseData->setBankAccountCreated($this->bankAccountCreated);
        $dDvResponseData->setCompanyRegistrationNumber($this->companyRegistrationNumber);
        $dDvResponseData->setNameScore($this->nameScore);
        $dDvResponseData->setAddressScore($this->addressScore);
        $dDvResponseData->setProprietorScore($this->proprietorScore);
        $dDvResponseData->setRegistrationMatch($this->registrationMatch);
        $dDvResponseData->setAccountStatus($this->accountStatus);
        $dDvResponseData->setNamePassScore($this->namePassScore);
        $dDvResponseData->setAddressPassScore($this->addressPassScore);
        $dDvResponseData->setProprietorPassScore($this->proprietorPassScore);
        $dDvResponseData->setRegistrationPassValue($this->registrationPassValue);
        $dDvResponseData->setCompanyNamePassScore($this->companyNamePassScore);
        $dDvResponseData->setCompanyAddressPassScore($this->companyAddressPassScore);
        $dDvResponseData->setExpirationTimer($this->expirationTimer);
        $dDvResponseData->setVerificationApplied($this->verificationApplied);
        $dDvResponseData->setHasPassedValidation(); //Note this is calculated from above-set values, not set directly

        $this->assertPopulatedObjectMatchesExpectedValues($dDvResponseData);
    }

    /**
     * Test fromArray works properly when proper array provided
     *
     * @return void
     */
    public function testFromArrayWorksProperlyWhenProperArrayProvided()
    {
        $dDvResponseData = DirectDebitVerificationResponseData::fromArray(
            $this->getArrayRepresentation()
        );

        $this->assertPopulatedObjectMatchesExpectedValues($dDvResponseData);
    }

    /**
     * Test fromArray works properly when no data provided
     *
     * @return void
     */
    public function testFromArrayWorksProperlyWhenNoDataProvided()
    {
        $this->setExpectedException(
            DirectDebitVerificationException::class,
            'ResponseData provided to '
            . 'Plusnet\DirectDebitVerification\Model\DirectDebitVerificationResponseData::fromArray is not an array '
            . '(responseData=NULL)!'
        );

        $mockAuditLogHelper = $this->getMockBuilder(
            AuditLogHelper::class
        )
            ->setMethods(['log'])
            ->disableOriginalConstructor()
            ->getMock();

        AuditLogHelper::setInstance($mockAuditLogHelper);

        DirectDebitVerificationResponseData::fromArray(null);

        AuditLogHelper::clearInstance();
    }

    /**
     * Test saveToDatabase works properly
     *
     * @return void
     */
    public function testSaveToDatabaseWorksProperly()
    {
        $dDvResponseData = DirectDebitVerificationResponseData::fromArray(
            $this->getArrayRepresentation()
        );

        $mockDbAdapter = $this->getMockBuilder(\Db_Adaptor::class)
            ->setMethods(['saveDDVerificationResponseToDatabase', 'getAffectedRows'])
            ->setConstructorArgs(
                [
                    'DirectDebitVerification',
                    self::DIRECT_DEBIT_VERIFICATION_TRANSACTION,
                    true
                ]
            )
            ->getMock();

        $mockDbAdapter->expects($this->once())
            ->method(('saveDDVerificationResponseToDatabase'))
            ->with(
                $this->callback(function ($data) use ($dDvResponseData) {
                    $dataEncHelper = DataEncryptionHelper::getInstance();
                    $this->assertEquals(
                        base64_encode(serialize($dDvResponseData)),
                        $dataEncHelper->decrypt($data),
                        'Not receiving expected encrypted, base64-encoded, serialised ddv RequestData object'
                    );

                    return true; // If assert fails, test will halt before callback returns true
                }),                                              // arg[0]
                $dDvResponseData->getVerificationApplied(),
                $dDvResponseData->haveDetailsPassedValidation(),
                $dDvResponseData->getValidationHash(),
                $dDvResponseData->getAccountId()
            );

        $mockDbAdapter->expects($this->once())
            ->method(('getAffectedRows'))
            ->willReturn(1);

        \Db_Manager::setAdaptor(
            'DirectDebitVerification',
            $mockDbAdapter,
            self::DIRECT_DEBIT_VERIFICATION_TRANSACTION
        );

        $this->assertEquals(1, $dDvResponseData->saveToDatabase());

        \Db_Manager::reset();
    }

    /**
     * Helper function to assert populated ddv response data object has expected values from this class's variables
     *
     * @param DirectDebitVerificationResponseData $dDvResponseData Populated ddv response data object
     *
     * @return void
     */
    private function assertPopulatedObjectMatchesExpectedValues(DirectDebitVerificationResponseData $dDvResponseData)
    {
        $this->assertEquals($this->validationHash, $dDvResponseData->getValidationHash());
        $this->assertEquals($this->accountId, $dDvResponseData->getAccountId());
        $this->assertEquals($this->id, $dDvResponseData->getId());
        $this->assertEquals($this->created, $dDvResponseData->getCreated());
        $this->assertEquals($this->lastUpdated, $dDvResponseData->getLastUpdated());
        $this->assertEquals($this->stage, $dDvResponseData->getStage());
        $this->assertEquals($this->company, $dDvResponseData->getCompany());
        $this->assertEquals($this->verificationStatus, $dDvResponseData->getVerificationStatus());
        $this->assertEquals(
            $this->currentAddressRecomendedStatus,
            $dDvResponseData->getCurrentAddressRecomendedStatus()
        );
        $this->assertEquals($this->formId, $dDvResponseData->getFormId());
        $this->assertEquals($this->formTypeId, $dDvResponseData->getFormTypeId());
        $this->assertEquals($this->formType, $dDvResponseData->getFormType());
        $this->assertEquals($this->title, $dDvResponseData->getTitle());
        $this->assertEquals($this->firstName, $dDvResponseData->getFirstName());
        $this->assertEquals(new \Val_Name($this->firstName), $dDvResponseData->getValidatedFirstName());
        $this->assertEquals($this->middleName, $dDvResponseData->getMiddleName());
        $this->assertEquals($this->lastName, $dDvResponseData->getLastName());
        $this->assertEquals(new \Val_Name($this->lastName), $dDvResponseData->getValidatedLastName());
        $this->assertEquals($this->dOB, $dDvResponseData->getDOB());
        $this->assertEquals($this->email, $dDvResponseData->getEmail());
        $this->assertEquals($this->mobile, $dDvResponseData->getMobile());
        $this->assertEquals($this->applyingAsCompany, $dDvResponseData->getApplyingAsCompany());
        $this->assertEquals($this->companyName, $dDvResponseData->getCompanyName());
        $this->assertEquals($this->currentHouseNameNumber, $dDvResponseData->getCurrentHouseNameNumber());
        $this->assertEquals($this->currentStreet1, $dDvResponseData->getCurrentStreet1());
        $this->assertEquals($this->currentStreet2, $dDvResponseData->getCurrentStreet2());
        $this->assertEquals($this->currentTown, $dDvResponseData->getCurrentTown());
        $this->assertEquals($this->currentPostcode, $dDvResponseData->getCurrentPostcode());
        $this->assertEquals($this->currentCountry, $dDvResponseData->getCurrentCountry());
        $this->assertEquals($this->bankAccountName, $dDvResponseData->getBankAccountName());
        $this->assertEquals($this->sortCode, $dDvResponseData->getSortCode());
        $this->assertEquals(
            new \Val_DirectDebit_SortCode(
                substr($this->sortCode, 0, 2),
                substr($this->sortCode, 2, 2),
                substr($this->sortCode, 4, 2)
            ),
            $dDvResponseData->getValidatedSortCode()
        );
        $this->assertEquals($this->accountNumber, $dDvResponseData->getAccountNumber());
        $this->assertEquals(
            new \Val_DirectDebit_AccountNumber($this->accountNumber),
            $dDvResponseData->getValidatedAccountNumber()
        );
        $this->assertEquals($this->ptxProfileName, $dDvResponseData->getPtxProfileName());
        $this->assertEquals($this->ddDebtorReference, $dDvResponseData->getDdDebtorReference());
        $this->assertEquals($this->ddPlanAltReference, $dDvResponseData->getDdPlanAltReference());
        $this->assertEquals($this->ddPlanReference, $dDvResponseData->getDdPlanReference());
        $this->assertEquals($this->ddPlanSpecification, $dDvResponseData->getDdPlanSpecification());
        $this->assertEquals($this->ddNoOfCollections, $dDvResponseData->getDdNoOfCollections());
        $this->assertEquals($this->ddRegularAmount, $dDvResponseData->getDdRegularAmount());
        $this->assertEquals($this->ddFirstAmount, $dDvResponseData->getDdFirstAmount());
        $this->assertEquals($this->ddLastAmount, $dDvResponseData->getDdLastAmount());
        $this->assertEquals($this->ddStartDate, $dDvResponseData->getDdStartDate());
        $this->assertEquals($this->ddPlanEndByDate, $dDvResponseData->getDdPlanEndByDate());
        $this->assertEquals($this->planEndByDate, $dDvResponseData->getPlanEndByDate());
        $this->assertEquals($this->planEndType, $dDvResponseData->getPlanEndType());
        $this->assertEquals($this->customData, $dDvResponseData->getCustomData());
        $this->assertEquals($this->formName, $dDvResponseData->getFormName());
        $this->assertEquals($this->giftAid, $dDvResponseData->getGiftAid());
        $this->assertEquals($this->bankName, $dDvResponseData->getBankName());
        $this->assertEquals($this->bankAccountCreated, $dDvResponseData->getBankAccountCreated());
        $this->assertEquals($this->companyRegistrationNumber, $dDvResponseData->getCompanyRegistrationNumber());
        $this->assertEquals($this->nameScore, $dDvResponseData->getNameScore());
        $this->assertEquals($this->addressScore, $dDvResponseData->getAddressScore());
        $this->assertEquals($this->proprietorScore, $dDvResponseData->getProprietorScore());
        $this->assertEquals($this->registrationMatch, $dDvResponseData->getRegistrationMatch());
        $this->assertEquals($this->accountStatus, $dDvResponseData->getAccountStatus());
        $this->assertEquals($this->namePassScore, $dDvResponseData->getNamePassScore());
        $this->assertEquals($this->addressPassScore, $dDvResponseData->getAddressPassScore());
        $this->assertEquals($this->proprietorPassScore, $dDvResponseData->getProprietorPassScore());
        $this->assertEquals($this->registrationPassValue, $dDvResponseData->getRegistrationPassValue());
        $this->assertEquals($this->companyNamePassScore, $dDvResponseData->getCompanyNamePassScore());
        $this->assertEquals($this->companyAddressPassScore, $dDvResponseData->getCompanyAddressPassScore());
        $this->assertEquals($this->expirationTimer, $dDvResponseData->getExpirationTimer());
        $this->assertTrue($dDvResponseData->getVerificationApplied());
        $this->assertEquals($this->hasPassedValidation, $dDvResponseData->getHasPassedValidation());
    }

    /**
     * Test setVerificationApplied properly set value
     *
     * @param string|bool|int|null $verificationApplied VerificationApplied received in callback payload
     * @param bool $expectedValue Expected value set by setVerificationApplied
     *
     * @dataProvider provideDataForTestSetVerificationAppliedProperlySetValue
     *
     * @return void
     */
    public function testSetVerificationAppliedProperlySetValue($verificationApplied, $expectedValue)
    {
        $ddvResponseData = DirectDebitVerificationResponseData::fromArray(
            ['VerificationApplied' => $verificationApplied]
        );
        $this->assertEquals($expectedValue, $ddvResponseData->getVerificationApplied());
    }

    /*
     * Provide data for testSetVerificationAppliedProperlySetValue
     */
    public function provideDataForTestSetVerificationAppliedProperlySetValue()
    {
        return [
            'VerificationApplied send as "true" - expects true' => [
                'verificationApplied' => 'true',
                'expectedValue' => true
            ],
            'VerificationApplied send as "True" - expects true' => [
                'verificationApplied' => 'True',
                'expectedValue' => true
            ],
            'VerificationApplied send as true - expects true' => [
                'verificationApplied' => true,
                'expectedValue' => true
            ],
            'VerificationApplied send as 1 - expects true' => [
                'verificationApplied' => 1,
                'expectedValue' => true
            ],
            'VerificationApplied send as "1" - expects true' => [
                'verificationApplied' => '1',
                'expectedValue' => true
            ],
            'VerificationApplied send as "false" - expects false' => [
                'verificationApplied' => 'false',
                'expectedValue' => false
            ],
            'VerificationApplied send as "False" - expects false' => [
                'verificationApplied' => 'False',
                'expectedValue' => false
            ],
            'VerificationApplied send as false - expects false' => [
                'verificationApplied' => false,
                'expectedValue' => false
            ],
            'VerificationApplied send as 0 - expects false' => [
                'verificationApplied' => 0,
                'expectedValue' => false
            ],
            'VerificationApplied send as "0" - expects false' => [
                'verificationApplied' => '0',
                'expectedValue' => false
            ],
            'VerificationApplied send as "xyz" - expects false' => [
                'verificationApplied' => 'xyz',
                'expectedValue' => false
            ],
            'VerificationApplied send as "-" - expects false' => [
                'verificationApplied' => '-',
                'expectedValue' => false
            ],
            'VerificationApplied send as "" - expects false' => [
                'verificationApplied' => '',
                'expectedValue' => false
            ],
            'VerificationApplied send as null - expects false' => [
                'verificationApplied' => null,
                'expectedValue' => false
            ]
        ];
    }
}
