<?php
/**
 * Account DAO Database Test
 *
 * @package    Core
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2012 Plusnet
 * @since      File available since 2012-02-08
 */
/**
 * Database test file for Core_AccountDao
 *
 * @package    Core
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2012 Plusnet
 */
class Core_AccountDaoDbTest extends Plusnet_Database_TestCase
{
    protected $arrServers = array(
        'Coredb_master' => array(),
        'Coredb_slave' => array()
    );


    protected $dbName = 'userdata';

    public function getDataSet()
    {
        return $this->createXmlDataSet(dirname(__FILE__) . '/../dataset/AccountsTestData.xml');
    }

    protected function createDatabaseStructure()
    {
        $this->runScriptsFromDirs(array(dirname(__FILE__) . '/../datastructure/'));
    }

    /**
     * PHPUnit tearDown functionality
     *
     * (non-PHPdoc)
     * @see Plusnet_Database_TestCase::tearDown()
     *
     * @return void
     */
    public function tearDown()
    {
        Db_Manager::commit(Db_Manager::DEFAULT_TRANSACTION);
        Db_Manager::restoreAdaptor('Core', Db_Manager::DEFAULT_TRANSACTION);
        parent::tearDown();
        \Db_Server::$decryptedPasswords = [];

    }

    /**
     * Set up unit test fixtures
     *
     * @return void
     */
    public function setup()
    {
        parent::setUp();
        \Db_Server::$decryptedPasswords['root'] = '';
    }

    /**
     * Test that the Core_AccountDao correctly retreives data from the userdata.accounts table.
     *
     * @dataProvider dataProviderForTestAccountDataRetrieved
     *
     * @covers Core_AccountDao::get
     * @covers Core_AccountDao::getAccountId
     * @covers Core_AccountDao::getCustomerId
     * @covers Core_AccountDao::getAddressId
     * @covers Core_AccountDao::getBalance
     * @covers Core_AccountDao::getTerms
     * @covers Core_AccountDao::getCreditLimit
     * @covers Core_AccountDao::getInvoiceType
     *
     * @param integer $accountId           Account ID
     * @param array   $expectedAccountData Array of expected Account data or null
     * @param array   $expectedException   Array of expected exception details or null
     *
     * @return void
     */
    public function testAccountDataRetrieved($accountId, $expectedAccountData, $expectedException)
    {
        if (!empty($expectedException)) {

            $this->setExpectedException($expectedException['type'], $expectedException['message']);
        }

        $accountData = Core_AccountDao::get($accountId);

        if (!empty($expectedAccountData)) {

            $this->assertEquals($expectedAccountData['accountId'], $accountData->getAccountId());
            $this->assertEquals($expectedAccountData['customerId'], $accountData->getCustomerId());
            $this->assertEquals($expectedAccountData['addressId'], $accountData->getAddressId());
            $this->assertEquals($expectedAccountData['balance'], $accountData->getBalance());
            $this->assertEquals($expectedAccountData['terms'], $accountData->getTerms());
            $this->assertEquals($expectedAccountData['creditLimit'], $accountData->getCreditLimit());
            $this->assertEquals($expectedAccountData['invoiceType'], $accountData->getInvoiceType());
        }
    }

    /**
     * Data provider for testAccountDataRetrieved
     *
     * @return array
     */
    public function dataProviderForTestAccountDataRetrieved()
    {
        return array(
            // Account with a VAT invoice type
            array(
                'accountId' => 1,
                'expectedAccountData' => array(
                    'accountId' => 1,
                    'customerId' => 2,
                    'addressId' => 3,
                    'balance' => 4.5,
                    'terms' => 'NONE',
                    'creditLimit' => 5,
                    'invoiceType' => 'VAT',
                ),
                'expectedException' => null,
            ),
            // Account with a NON VAT invoice type
            array(
                'accountId' => 2,
                'expectedAccountData' => array(
                    'accountId' => 2,
                    'customerId' => 3,
                    'addressId' => 4,
                    'balance' => 5.6,
                    'terms' => 'NONE',
                    'creditLimit' => 6,
                    'invoiceType' => 'NON_VAT',
                ),
                'expectedException' => null,
            ),
            // Non existant account
            array(
                'accountId' => 99,
                'expectedAccountData' => null,
                'expectedException' => array(
                    'type' => 'Db_ObjectException',
                    'message' => 'getAccountDao',
                ),
            ),
        );
    }
}