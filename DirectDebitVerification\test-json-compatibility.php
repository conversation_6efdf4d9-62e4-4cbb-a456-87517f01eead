<?php
/**
 * Test script for JSON compatibility between PHP 8.2 and PHP 5.6
 * 
 * This script tests the DirectDebitData::toJson() method and other JSON encoding
 * to ensure compatibility with PHP 5.6 FrameworkWebApi
 */

// Include the necessary files
require_once __DIR__ . '/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php';
require_once __DIR__ . '/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php';
require_once __DIR__ . '/src/Plusnet/DirectDebitVerification/Helpers/AuditLogHelper.php';

use Plusnet\DirectDebitVerification\Model\DirectDebitData;
use Plusnet\DirectDebitVerification\Helpers\JsonCompatibilityHelper;

echo "DD-Verification-Service JSON Compatibility Test\n";
echo "==============================================\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Testing JSON encoding compatibility with PHP 5.6\n\n";

// Test 1: Basic DirectDebitData object
echo "Test 1: DirectDebitData::toJson() method\n";
echo "---------------------------------------\n";

// Create a test class that extends DirectDebitData
class TestDirectDebitData extends DirectDebitData
{
    public $testField = 'Test value with special chars: "quotes" and –dashes—';
    public $unicodeField = 'Unicode: € £ © ® ™';
    public $controlField = "Control chars: \x01\x02\x03";
    public $replacementField = "Replacement char: \xEF\xBF\xBD";
    public $normalField = 'Normal ASCII text';
    
    public static $maskingRules = [
        'testField' => ['pattern' => '/special/']
    ];
}

$testObject = new TestDirectDebitData();

echo "Original object properties:\n";
foreach (get_object_vars($testObject) as $key => $value) {
    echo "  $key: " . var_export($value, true) . "\n";
}

// Test the toJson method
echo "\nTesting toJson() method:\n";
$jsonResult = $testObject->toJson(false); // Don't mask for testing
echo "JSON result: " . $jsonResult . "\n";

// Test if the JSON can be decoded
$decoded = json_decode($jsonResult, true);
if ($decoded === null && json_last_error() !== JSON_ERROR_NONE) {
    echo "ERROR: JSON cannot be decoded: " . json_last_error_msg() . "\n";
} else {
    echo "SUCCESS: JSON can be decoded properly\n";
}

// Test 2: JsonCompatibilityHelper
echo "\n\nTest 2: JsonCompatibilityHelper::jsonEncodeForPhp56()\n";
echo "---------------------------------------------------\n";

$testData = [
    'name' => 'Test User',
    'description' => 'Test with special chars: "smart quotes" and –dashes—',
    'unicode' => 'Unicode: € £ © ® ™',
    'control' => "Control chars: \x01\x02\x03",
    'replacement' => "Replacement char: \xEF\xBF\xBD",
    'nested' => [
        'field1' => 'value1',
        'field2' => 'value with "quotes"'
    ]
];

echo "Test data:\n";
var_dump($testData);

// Test standard json_encode
$standardJson = json_encode($testData);
echo "\nStandard json_encode result:\n";
echo $standardJson . "\n";

// Test compatibility helper
$compatibleJson = JsonCompatibilityHelper::jsonEncodeForPhp56($testData, true);
echo "\nCompatible json_encode result:\n";
echo $compatibleJson . "\n";

// Test 3: Compatibility test
echo "\n\nTest 3: Comprehensive compatibility test\n";
echo "--------------------------------------\n";

$testResults = JsonCompatibilityHelper::testJsonCompatibility($testData);

echo "Original JSON decodable: " . ($testResults['original_decodable'] ? 'YES' : 'NO') . "\n";
if (!$testResults['original_decodable']) {
    echo "Original JSON error: " . $testResults['original_error'] . "\n";
}

echo "Compatible JSON decodable: " . ($testResults['compatible_decodable'] ? 'YES' : 'NO') . "\n";
if (!$testResults['compatible_decodable']) {
    echo "Compatible JSON error: " . $testResults['compatible_error'] . "\n";
}

// Test 4: Character encoding test
echo "\n\nTest 4: Character encoding test\n";
echo "------------------------------\n";

$problematicStrings = [
    'Unicode replacement: �',
    'Smart quotes: "hello"',
    'Em dash: —',
    'Euro symbol: €',
    'Control chars: ' . "\x01\x02\x03",
    'Mixed: "Hello" — this costs €50'
];

foreach ($problematicStrings as $i => $string) {
    echo "String " . ($i + 1) . ": " . bin2hex($string) . "\n";
    echo "  Original: " . $string . "\n";
    
    $cleaned = JsonCompatibilityHelper::cleanDataForPhp56($string);
    echo "  Cleaned:  " . $cleaned . "\n";
    
    $json = JsonCompatibilityHelper::jsonEncodeForPhp56($string);
    echo "  JSON:     " . $json . "\n";
    
    $decoded = json_decode($json);
    if ($decoded === null && json_last_error() !== JSON_ERROR_NONE) {
        echo "  ERROR: Cannot decode JSON: " . json_last_error_msg() . "\n";
    } else {
        echo "  SUCCESS: JSON decoded as: " . $decoded . "\n";
    }
    echo "\n";
}

// Test 5: Real-world scenario simulation
echo "\nTest 5: Real-world DirectDebit verification data\n";
echo "-----------------------------------------------\n";

// Simulate a real DirectDebitVerificationResponseData object
class MockDirectDebitVerificationResponseData extends DirectDebitData
{
    public $validationHash = 'abc123def456';
    public $accountId = 12345;
    public $firstName = 'John';
    public $lastName = 'O'Connor'; // Apostrophe test
    public $companyName = 'Test & Company Ltd'; // Ampersand test
    public $verificationStatus = 'success';
    public $created = '2025-01-03 10:30:00';
    public $notes = 'Customer provided "correct" details — verification passed';
    
    public static $maskingRules = [
        'firstName' => ['pattern' => '/John/'],
        'lastName' => ['pattern' => '/Connor/']
    ];
}

$mockResponse = new MockDirectDebitVerificationResponseData();

echo "Mock response object:\n";
foreach (get_object_vars($mockResponse) as $key => $value) {
    echo "  $key: " . var_export($value, true) . "\n";
}

echo "\nJSON representation (unmasked):\n";
$responseJson = $mockResponse->toJson(false);
echo $responseJson . "\n";

echo "\nJSON representation (masked):\n";
$maskedJson = $mockResponse->toJson(true);
echo $maskedJson . "\n";

// Verify both can be decoded
$unmaskedDecoded = json_decode($responseJson, true);
$maskedDecoded = json_decode($maskedJson, true);

if ($unmaskedDecoded === null && json_last_error() !== JSON_ERROR_NONE) {
    echo "ERROR: Unmasked JSON cannot be decoded: " . json_last_error_msg() . "\n";
} else {
    echo "SUCCESS: Unmasked JSON decoded properly\n";
}

if ($maskedDecoded === null && json_last_error() !== JSON_ERROR_NONE) {
    echo "ERROR: Masked JSON cannot be decoded: " . json_last_error_msg() . "\n";
} else {
    echo "SUCCESS: Masked JSON decoded properly\n";
}

echo "\n\nTest completed!\n";
echo "If all tests show SUCCESS, the JSON compatibility fix is working.\n";
echo "If you see any ERROR messages, there may still be compatibility issues.\n";
