<?php
/**
 * Class TPARFilterTest
 *
 * @package    Framework
 * @subpackage Auth
 * <AUTHOR> <<EMAIL>>
 * @since      October 2014
 */

require_once 'Filter_TestCase.php';

/**
 * Class TPARFilterTest
 *
 * @package    Framework
 * @subpackage Auth
 * <AUTHOR> <<EMAIL>>
 * @since      October 2014
 */
class TPARFilterTest extends Filter_TestCase
{
    /**
     * Tests that filter indicates redirect required if there is failed billing
     *
     * @covers Auth_TPARFilter
     * @return void
     */
    public function testShouldIndicateRedirectRequiredOnFailedBilling()
    {
        $mockHttpRequest = $this->createFullyMockedObject('Mvc_HttpRequest');
        $mockRequestRequirements = $this->createFullyMockedObject('Mvc_RequestRequirements');
        $mockLogin = $this->createMockLoginWithChildAccounts();
        $this->setupMockDbAdaptorWithFailedBillingSetTo(true);

        $this->assertTrue(
            Auth_TPARFilter::checkFilter($mockHttpRequest, $mockRequestRequirements, $mockLogin)
        );
    }

    /**
     * Tests that filter indicates redirect is not required if there is no failed billing
     *
     * @covers Auth_TPARFilter
     * @return void
     */
    public function testShouldIndicateRedirectNotRequiredWhenNoFailedBilling()
    {
        $mockHttpRequest = $this->createFullyMockedObject('Mvc_HttpRequest');
        $mockRequestRequirements = $this->createFullyMockedObject('Mvc_RequestRequirements');
        $mockLogin = $this->createMockLoginWithChildAccounts();
        $this->setupMockDbAdaptorWithFailedBillingSetTo(false);

        $this->assertFalse(
            Auth_TPARFilter::checkFilter($mockHttpRequest, $mockRequestRequirements, $mockLogin)
        );
    }

    /**
     * Sets up mock Db_Adaptor and sets number of faults to report
     *
     * @param bool $failedBilling Should report failed billing or not
     *
     * @return void
     */
    protected function setupMockDbAdaptorWithFailedBillingSetTo($failedBilling)
    {
        if ($failedBilling) {
            $result = array(1, 2, 3);
        } else {
            $result = array();
        }
        $this->setUpMockDbAdaptor(
            'Auth',
            array(
                'getFailedBilling' => array(1, $result)
            )
        );
    }
}
