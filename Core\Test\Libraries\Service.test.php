<?php
/**
 * Service
 *
 * Testing class for the Core_Service
 *
 * @package    Core
 * <AUTHOR> <lchu<PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright  2009 PlusNet
 * @since      File available since 2009-09-21
 */
/**
 * Service Test
 *
 * @package    Core
 * <AUTHOR> <lchu<PERSON><PERSON><PERSON>@plus.net>
 *
 * @copyright  2009 PlusNet
 */
class Core_Service_Test extends PHPUnit_Framework_TestCase
{
    /** @var \Plusnet\Feature\FeatureTogglePersistence|PHPUnit_Framework_MockObject_MockObject */
    private $mockPersistenceAdaptor;

    public function setUp()
    {
        $this->mockPersistenceAdaptor = $this->getMockBuilder(
            'Plusnet\Feature\FeatureTogglePersistence'
        )
            ->setMethods(array('getToggleByName','isUsernameExcludedFromFeatureSwitch'))
            ->getMockForAbstractClass();
    }
	/**
	 * Restore the default transaction, and restore the adaptors we over-ride
	 *
	 * @see Framework/PHPUnit_Framework_TestCase#tearDown()
	 */
	public function tearDown()
	{
		//Reset transaction and claer db object cache
		Db_Object::signalTransactionEnd(Db_Manager::DEFAULT_TRANSACTION);

		Db_Manager::restoreAdaptor('Core', Db_Manager::DEFAULT_TRANSACTION);

        Core_Service::reset();
	}

    /**
     * testFetchByServiceIdReturnsCoreServiceObject
     *
     * @covers Core_Service::fetchByServiceId
     * @covers Core_Service::cacheService
     *
     * @return void
     */
    public function testFetchByServiceIdReturnsCoreServiceObject()
    {
        $service = array(
            'service_id' => 123456,
        );

        $coreAdp = $this->getMock(
            'Db_Adaptor',
            array('getServiceDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $coreAdp
            ->expects($this->once())
            ->method('getServiceDao')
            ->with($service['service_id'])
            ->will($this->returnValue($service));
        Db_Manager::setAdaptor('Core', $coreAdp, Db_Manager::DEFAULT_TRANSACTION);

        $service = Core_Service::fetchByServiceId($service['service_id']);
        $this->assertThat($service, $this->isInstanceOf('Core_Service'));
    }

    /**
     * testFetchByServiceIdCachesObject
     *
     * @covers Core_Service::fetchByServiceId
     * @covers Core_Service::cacheService
     *
     * @return void
     */
    public function testFetchByServiceIdCachesObject()
    {
        $service = array(
            'service_id' => 123456,
        );

        $coreAdp = $this->getMock(
            'Db_Adaptor',
            array('getServiceDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $coreAdp
            ->expects($this->once())
            ->method('getServiceDao')
            ->with($service['service_id'])
            ->will($this->returnValue($service));
        Db_Manager::setAdaptor('Core', $coreAdp, Db_Manager::DEFAULT_TRANSACTION);

        $service1 = Core_Service::fetchByServiceId($service['service_id']);
        $this->assertThat($service1, $this->isInstanceOf('Core_Service'));

        $service2 = Core_Service::fetchByServiceId($service['service_id']);
        $this->assertEquals($service1, $service2);
    }

    /**
     * testFetchByServiceIdCachesObject
     *
     * @covers Core_Service::fetchByServiceId
     * @covers Core_Service::cacheService
     * @covers Core_Service::setServiceCacheSize
     *
     * @return void
     */
    public function testFetchByServiceIdCachesObjectUnsetsFirstObject()
    {
        Core_Service::setServiceCacheSize(1);

        $service1 = array(
            'service_id' => 123456,
        );
        $service2 = array(
            'service_id' => 123457,
        );

        $coreAdp = $this->getMock(
            'Db_Adaptor',
            array('getServiceDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $coreAdp
            ->expects($this->at(0))
            ->method('getServiceDao')
            ->with($service1['service_id'])
            ->will($this->returnValue($service1));
        $coreAdp
            ->expects($this->at(1))
            ->method('getServiceDao')
            ->with($service2['service_id'])
            ->will($this->returnValue($service2));
        Db_Manager::setAdaptor('Core', $coreAdp, Db_Manager::DEFAULT_TRANSACTION);

        $coreService1 = Core_Service::fetchByServiceId($service1['service_id']);
        $this->assertEquals($service1['service_id'], $coreService1->getServiceId());

        $coreService2 = Core_Service::fetchByServiceId($service2['service_id']);
        $this->assertEquals($service2['service_id'], $coreService2->getServiceId());

        // We reset the DB layer here to ensure it's not caching there instead
		Db_Object::signalTransactionEnd(Db_Manager::DEFAULT_TRANSACTION);
		Db_Manager::restoreAdaptor('Core', Db_Manager::DEFAULT_TRANSACTION);

        $coreAdp = $this->getMock(
            'Db_Adaptor',
            array('getServiceDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $coreAdp
            ->expects($this->once())
            ->method('getServiceDao')
            ->with($service1['service_id'])
            ->will($this->returnValue($service1));
        Db_Manager::setAdaptor('Core', $coreAdp, Db_Manager::DEFAULT_TRANSACTION);

        // Check we get the second record back from cache
        $this->assertEquals($coreService2, Core_Service::fetchByServiceId($service2['service_id']));

        // First record needs to be queried again
        $coreService1b = Core_Service::fetchByServiceId($service1['service_id']);
        $this->assertEquals($service1['service_id'], $coreService1b->getServiceId());
        $this->assertEquals($coreService1->getServiceId(), $coreService1b->getServiceId());
    }

    /**
     * testCacheServiceCanSetMockObject
     *
     * @covers Core_Service::fetchByServiceId
     * @covers Core_Service::cacheService
     *
     * @return void
     */
    public function testCacheServiceCanSetMockObject()
    {
        $service = array(
            'service_id' => '123456',
        );

        $mockService = $this->getMock(
            'Core_Service',
            array('getServiceId'),
            array(), '', false
        );
        $mockService
            ->expects($this->once())
            ->method('getServiceId')
            ->will($this->returnValue($service['service_id']));

        Core_Service::cacheService($mockService);

        $gotService = Core_Service::fetchByServiceId($service['service_id']);
        $this->assertEquals($mockService, $gotService);
    }

    /**
     * testGetBillingDate
     *
     * @param bool $rbmMigrationComplete whether the RBM migration has completed
     * @covers Core_Service::getBillingDate
     * @dataProvider rbmMigrationCompleteDataProvider
     * @return void
     */
    public function testGetBillingDate($rbmMigrationComplete)
    {
        $service = array(
            'service_id' => 123450,
            'next_invoice' => '2010-08-23',
            'invoice_period' => 'monthly',
            'invoice_day' => '23'
        );

        $coreAdp = $this->getMock(
            'Db_Adaptor',
            array('getServiceDao'),
            array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
        );
        $coreAdp
            ->expects($this->once())
            ->method('getServiceDao')
            ->with($service['service_id'])
            ->will($this->returnValue($service));
        Db_Manager::setAdaptor('Core', $coreAdp, Db_Manager::DEFAULT_TRANSACTION);

        $service = $this
            ->getMockBuilder('Core_Service')
            ->setMethods(['getNextInvoiceDate', 'isRbmMigrationComplete'])
            ->setConstructorArgs([$service['service_id']])
            ->getMock();
        $service
            ->expects($this->any())
            ->method('getNextInvoiceDate')
            ->willReturn(I18n_Date::fromString('2010-08-23'));
        $service
            ->expects($this->once())
            ->method('isRbmMigrationComplete')
            ->willReturn($rbmMigrationComplete);

        $this->assertThat($service, $this->isInstanceOf('Core_Service'));
        $billingDate = $service->getBillingDate();
        $this->assertThat($billingDate, $this->isInstanceOf('Core_BillingDate'));
    }

    /**
     * @return array
     */
    public function rbmMigrationCompleteDataProvider()
    {
        return [[true], [false]];
    }

	/**
	 * @covers Core_Service::getVispConfig
	 */
	public function testGetVispConfig()
	{
		$intServiceId = 1;

		$strIsp = 'plus.net';
		$strIspFullName = 'PlusNet';

		$arrService = array(
				'service_id' => $intServiceId,
				'isp' => $strIsp
		);

		$arrVispConfig = array(
				'intIspDefinitionId' => 1,
				'strIsp' => $strIsp,
				'strFullName' => $strIspFullName,
				'strShortBrand' => 'pn'
		);

		$dbAdaptor = $this->getMock(
				'Db_Adaptor',
				array('getServiceDao','getVispConfigDao'),
				array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
		);

		$dbAdaptor->expects($this->once())
		          ->method('getServiceDao')
		          ->will($this->returnValue($arrService));

		$dbAdaptor->expects($this->once())
		          ->method('getVispConfigDao')
		          ->will($this->returnValue($arrVispConfig));

		Db_Manager::setAdaptor('Core', $dbAdaptor);

		$objService = new Core_Service($intServiceId);

		$objVispConfig = $objService->getVispConfig();

		$this->assertClassHasAttribute('objVispConfig', 'Core_Service');
		$this->assertInstanceOf('Core_VispConfig', $objVispConfig);

		$strResult = $objVispConfig->getFullName();
		$this->assertEquals($strIspFullName, $strResult);
	}

	/**
	 * @covers Core_Service::isMaafUser
	 */
	public function testIsMaafUserReturnTrue()
	{
		$intServiceId = 2;

		$strIsp = 'madasafish';
		$strIspFullName = 'Madasafish';
		$strShortBrand = 'ma';

		$arrService = array(
				'service_id' => $intServiceId,
				'isp' => $strIsp
		);

		$arrVispConfig = array(
				'intIspDefinitionId' => 1,
				'strIsp' => $strIsp,
				'strFullName' => $strIspFullName,
				'strShortBrand' => $strShortBrand
		);

		$dbAdaptor = $this->getMock(
				'Db_Adaptor',
				array('getServiceDao','getVispConfigDao'),
				array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
		);

		$dbAdaptor->expects($this->once())
		          ->method('getServiceDao')
		          ->will($this->returnValue($arrService));

		$dbAdaptor->expects($this->once())
		          ->method('getVispConfigDao')
		          ->will($this->returnValue($arrVispConfig));

		Db_Manager::setAdaptor('Core', $dbAdaptor);

		$objService = new Core_Service($intServiceId);

		$this->assertTrue($objService->isMaafUser());
	}

	/**
	 * @covers Core_Service::isMaafUser
	 */
	public function testIsMaafUserReturnFalse()
	{
		$intServiceId = 3;

		$strIsp = 'plus.net';
		$strIspFullName = 'PlusNet';
		$strShortBrand = 'pn';

		$arrService = array(
				'service_id' => $intServiceId,
				'isp' => $strIsp
		);

		$arrVispConfig = array(
				'intIspDefinitionId' => 1,
				'strIsp' => $strIsp,
				'strFullName' => $strIspFullName,
				'strShortBrand' => $strShortBrand
		);

		$dbAdaptor = $this->getMock(
				'Db_Adaptor',
				array('getServiceDao','getVispConfigDao'),
				array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
		);

		$dbAdaptor->expects($this->once())
		          ->method('getServiceDao')
		          ->will($this->returnValue($arrService));

		$dbAdaptor->expects($this->once())
		          ->method('getVispConfigDao')
		          ->will($this->returnValue($arrVispConfig));

		Db_Manager::setAdaptor('Core', $dbAdaptor);

		$objService = new Core_Service($intServiceId);

		$this->assertFalse($objService->isMaafUser());
	}

	/**
	 * @covers Core_Service::isMaafUser
	 */
	public function testIsMaafUserThrowsCoreException()
	{
		$intServiceId = 4;

		$strIsp = 'plus.net';
		$strIspFullName = 'PlusNet';
		$strShortBrand = '';

		$arrService = array(
				'service_id' => $intServiceId,
				'isp' => $strIsp
		);

		$arrVispConfig = array(
				'intIspDefinitionId' => 1,
				'strIsp' => $strIsp,
				'strFullName' => $strIspFullName,
				'strShortBrand' => $strShortBrand
		);

		$dbAdaptor = $this->getMock(
				'Db_Adaptor',
				array('getServiceDao','getVispConfigDao'),
				array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
		);

		$dbAdaptor->expects($this->once())
		          ->method('getServiceDao')
		          ->will($this->returnValue($arrService));

		$dbAdaptor->expects($this->once())
		          ->method('getVispConfigDao')
		          ->will($this->returnValue($arrVispConfig));

		Db_Manager::setAdaptor('Core', $dbAdaptor);

		$objService = new Core_Service($intServiceId);

		$this->setExpectedException(
				'Core_Exception',
				'Cannot identify short brand - got empty string',
				Core_Exception::ERR_EMPTY_SHORT_BRAND
		);

		$objService->isMaafUser();
	}

	/**
	 * @covers Core_Service::isBvUser
	 */
	public function testIsBvUserReturnTrue()
	{
		$intServiceId = 5;

		$strIsp = 'waitrose';
		$strIspFullName = 'Waitrose';
		$strShortBrand = 'wr';

		$arrService = array(
				'service_id' => $intServiceId,
				'isp' => $strIsp
		);

		$arrVispConfig = array(
				'intIspDefinitionId' => 1,
				'strIsp' => $strIsp,
				'strFullName' => $strIspFullName,
				'strShortBrand' => $strShortBrand
		);

		$dbAdaptor = $this->getMock(
				'Db_Adaptor',
				array (
					'getServiceDao',
					'getVispConfigDao',
					'getServiceComponentDetailsForService',
					'getServiceDefinitionDetailsForService'
				),
				array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
		);

		$dbAdaptor->expects($this->once())
		          ->method('getServiceDao')
		          ->will($this->returnValue($arrService));

		$dbAdaptor->expects($this->once())
		          ->method('getVispConfigDao')
		          ->will($this->returnValue($arrVispConfig));

		Db_Manager::setAdaptor('Core', $dbAdaptor);

		$objService = new Core_Service($intServiceId);

		$this->assertTrue($objService->isBvUser());
	}

	/**
	 * @covers Core_Service::isBvUser
	 */
	public function testIsBvUserReturnFalse()
	{
		$intServiceId = 6;

		$strIsp = 'plus.net';
		$strIspFullName = 'PlusNet';
		$strShortBrand = 'pn';

		$arrService = array(
				'service_id' => $intServiceId,
				'isp' => $strIsp
		);

		$arrVispConfig = array(
				'intIspDefinitionId' => 1,
				'strIsp' => $strIsp,
				'strFullName' => $strIspFullName,
				'strShortBrand' => $strShortBrand
		);

		$dbAdaptor = $this->getMock(
				'Db_Adaptor',
				array(
					'getServiceDao',
					'getVispConfigDao',
					'getServiceComponentDetailsForService',
					'getServiceDefinitionDetailsForService'
				),
				array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
		);

		$dbAdaptor->expects($this->once())
		          ->method('getServiceDao')
		          ->will($this->returnValue($arrService));

		$dbAdaptor->expects($this->once())
		          ->method('getVispConfigDao')
		          ->will($this->returnValue($arrVispConfig));

		Db_Manager::setAdaptor('Core', $dbAdaptor);

		$objService = new Core_Service($intServiceId);

		$this->assertFalse($objService->isBvUser());
	}

	/**
	 * @covers Core_Service::isBvUser
	 */
	public function testIsBvUserThrowsCoreException()
	{
		$intServiceId = 7;

		$strIsp = 'greenbee';
		$strIspFullName = 'Greenbee';
		$strShortBrand = '';

		$arrService = array(
				'service_id' => $intServiceId,
				'isp' => $strIsp
		);

		$arrVispConfig = array(
				'intIspDefinitionId' => 1,
				'strIsp' => $strIsp,
				'strFullName' => $strIspFullName,
				'strShortBrand' => $strShortBrand
		);

		$dbAdaptor = $this->getMock(
				'Db_Adaptor',
				array(
					'getServiceDao',
					'getVispConfigDao',
					'getServiceComponentDetailsForService',
					'getServiceDefinitionDetailsForService'
				),
				array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
		);

		$dbAdaptor->expects($this->once())
		          ->method('getServiceDao')
		          ->will($this->returnValue($arrService));

		$dbAdaptor->expects($this->once())
		          ->method('getVispConfigDao')
		          ->will($this->returnValue($arrVispConfig));

		Db_Manager::setAdaptor('Core', $dbAdaptor);

		$objService = new Core_Service($intServiceId);

		$this->setExpectedException(
				'Core_Exception',
				'Cannot identify short brand - got empty string',
				Core_Exception::ERR_EMPTY_SHORT_BRAND
		);

		$objService->isBvUser();
	}

    /**
     * Test that we get the report sector back
     *
     * @covers Core_service::getReportSector
     *
     * @return void
     */
    public function testGetReportSectorReturnsReportSector()
    {
        $intServiceId    = 8;
        $strIsp          = 'myisp';
        $strReportSector = 'myreportsector';

        $arrService = array(
            'service_id' => $intServiceId,
            'isp'        => $strIsp
        );

		$dbAdaptor = $this->getMock(
				'Db_Adaptor',
				array('getServiceDao','getReportSectorForService'),
				array('Core', Db_Manager::DEFAULT_TRANSACTION, false)
		);
		$dbAdaptor
            ->expects($this->once())
		    ->method('getServiceDao')
		    ->will($this->returnValue($arrService));
        $dbAdaptor
            ->expects($this->once())
            ->method('getReportSectorForService')
            ->will($this->returnValue($strReportSector));

        Db_Manager::setAdaptor('Core', $dbAdaptor);

        $objService = new Core_Service($intServiceId);

        $this->assertEquals($strReportSector, $objService->getReportSector());
    }

    /*
     * Method to give inputs for the test method dataForBillingDate
     * @return array of inputs
     */
    public function dataForBillingDate()
    {
        $date = new DateTime();
        $interval = new DateInterval('P1M');
        $date->add($interval);
        return array(
            array((new DateTime())->add(new DateInterval('P1M')), 'monthly', true),
            array((new DateTime())->add(new DateInterval('P2M')), 'monthly', false),
            array((new DateTime())->add(new DateInterval('P3M')), 'quarterly', true),
            array((new DateTime())->add(new DateInterval('P4M')), 'quarterly', false),
            array((new DateTime())->add(new DateInterval('P6M')), 'half-yearly', true)
        );
    }

    /**
     * Test that we got the nextInvoice date and check whether its today's date
     *
     * @dataProvider dataForBillingDate
     * @return void
     */
    public function testIsBillingToday($date, $period, $expected)
    {
        $isToday = Core_Service::checkDateToday($date, $period);
        $this->assertEquals($expected, $isToday);
    }

    /**
     * Test getting the next invoice date from coredb, not RBM
     *
     * @return void
     */
    public function testGetNextInvoiceDateNotRbm()
    {
        $endUserServiceId = 1234;
        $endUserInvoiceDate = '2017-05-20';

        $this->setRbmFeatureToggleOff();

        $this->mockGetServiceDao($this->equalTo($endUserServiceId), array(
            'isp' => 'plus.net',
            'next_invoice' => $endUserInvoiceDate,
        ));

        $mockHelper = $this->getMock("Reseller_CustomerHelper", array("isPartner"), array(), '', false);
        $mockHelper->expects($this->any())
            ->method("isPartner")
            ->willReturn(false);
        Reseller_CustomerHelper::setByServiceId($endUserServiceId, $mockHelper);

        $sut = new Core_Service($endUserServiceId);
        $expected = I18n_Date::fromString($endUserInvoiceDate);
        $this->assertEquals($expected, $sut->getNextInvoiceDate());
    }

    /**
     * Test getting the next invoice date from RBM
     *
     * @return void
     */
    public function testGetNextInvoiceDateForRbm()
    {
        $endUserServiceId = 1234;
        $endUserInvoiceDate = '2017-05-20'; // In the past at time of writing, so effects shouldn't change

        $this->setRbmFeatureToggleOn();

        // Mock CoreDB data, will set the ISP to plus.net
        $this->mockGetServiceDao($this->equalTo($endUserServiceId), array('isp' => 'plus.net'));

        // Mock the BillingApiFacade to return the billing data
        $billingApiFacade = $this->getMock("BillingApiFacade", array("getBillingAccountLite"));
        $billingApiFacade->expects($this->any())
            ->method("getBillingAccountLite")
            ->with($this->equalTo($endUserServiceId))
            ->willReturn(array(
                'nextInvoiceDate' => $endUserInvoiceDate
            ));
        \Plusnet\BillingApiClient\Service\ServiceManager::setService("BillingApiFacade", $billingApiFacade);

        $sut = new Core_Service($endUserServiceId);
        $expected = I18n_Date::fromString($endUserInvoiceDate);
        $this->assertEquals($expected, $sut->getNextInvoiceDate());
    }



    /**
     * Test getting the next invoice date from RBM and incrementing
     *
     * @dataProvider testNextNextInvoiceDateData
     *
     * @return void
     */
    public function testGetNextNextInvoiceDate($testInvoicePeriod, $nextNextInvoiceDate)
    {
        $endUserServiceId = 1234;
        $endUserInvoiceDate = '2017-05-20'; // In the past at time of writing, so effects shouldn't change
       // $nextNextInvoiceDate = '2017-06-20';

        $this->setRbmFeatureToggleOn();

        // Mock CoreDB data, will set the ISP to plus.net
        $this->mockGetServiceDao($this->equalTo($endUserServiceId), array('isp' => 'plus.net','invoice_period'=>$testInvoicePeriod));

        // Mock the BillingApiFacade to return the billing data
        $billingApiFacade = $this->getMock("BillingApiFacade", array("getBillingAccountLite"));
        $billingApiFacade->expects($this->any())
            ->method("getBillingAccountLite")
            ->with($this->equalTo($endUserServiceId))
            ->willReturn(array(
                'nextInvoiceDate' => $endUserInvoiceDate
            ));
        \Plusnet\BillingApiClient\Service\ServiceManager::setService("BillingApiFacade", $billingApiFacade);


        $sut = new Core_Service($endUserServiceId);
        $expected = I18n_Date::fromString($nextNextInvoiceDate);
        $this->assertEquals($expected, $sut->getNextNextInvoiceDate());
    }


    /**
     * Test data to pump into testGetNextNextInvoiceDate
     *
     * @return array
     */
    public function testNextNextInvoiceDateData()
    {
        return [
          ["monthly","2017-06-20"],
          ["never","2017-06-20"],
          ["quarterly", "2017-08-20"],
          ["half-yearly", "2017-11-20"],
          ["yearly", "2018-05-20"],
        ];
    }


    /**
     * Sets the RBM_MIGRATION_COMPLETE feature toggle ON for all users
     * Using $this->mockPersistenceAdaptor
     *
     * @return void
     */
    private function setRbmFeatureToggleOn()
    {
        $this->setRbmFeatureToggle(true);
        $this->assertTrue(\Plusnet\Feature\FeatureToggleManager::isOnFiltered("RBM_MIGRATION_COMPLETE", "myuser"));
    }

    /**
     * Sets the RBM_MIGRATION_COMPLETE feature toggle OFF for all users
     * Using $this->mockPersistenceAdaptor
     *
     * @return void
     */
    private function setRbmFeatureToggleOff()
    {
        $this->setRbmFeatureToggle(false);
        $this->assertFalse(\Plusnet\Feature\FeatureToggleManager::isOnFiltered("RBM_MIGRATION_COMPLETE", "myuser"));
    }

    /**
     * Sets the RBM_MIGRATION_COMPLETE feature toggle for all users
     * Using $this->mockPersistenceAdaptor
     *
     * @return void
     */
    private function setRbmFeatureToggle($on)
    {
        if ($on) {
            $offDateTime = time() + 100000;
        } else {
            $offDateTime = time() - 100000;
        }

        // Mock persistence adaptor, forces feature toggle on
        $this->mockPersistenceAdaptor->expects($this->any())
            ->method("getToggleByName")
            ->with($this->equalTo("RBM_MIGRATION_COMPLETE"))
            ->willReturn(array(
                'onDateTime' => date('Y-m-d', time() - 10000000),
                'offDateTime' => date('Y-m-d', $offDateTime),
            ));
        $this->mockPersistenceAdaptor->expects($this->any())
            ->method("isUsernameExcludedFromFeatureSwitch")
            ->willReturn(false);
        \Plusnet\Feature\FeatureToggleManager::setInstance($this->mockPersistenceAdaptor);
    }

    private function mockGetServiceDao(PHPUnit_Framework_Constraint $serviceIdConstraint, array $data)
    {
        /** @var Db_Adaptor|PHPUnit_Framework_MockObject_MockObject $adaptor */
        $adaptor = $this->getMock(
            "Db_Adaptor",
            array(
                "getServiceDao",
            ),
            array(
                "Core",
                Db_Manager::DEFAULT_TRANSACTION,
                false
            ));

        // getServiceDao is called when setting up Core_Service for the end user or the reseller
        // This is not under test, and may not be called (especially for the reseller)
        $adaptor->expects($this->any())
            ->method("getServiceDao")
            ->with($serviceIdConstraint)
            ->willReturnCallback(function($serviceId) use ($data)
            {
                return array_merge(array('service_id' => $serviceId), $data);
            });

        Db_Manager::setAdaptor("Core", $adaptor);
    }

    /**
     * @test
     * @dataProvider isJohnLewisVispCustomerReturnsCorrectResultsDataProvider
     *
     * @param string $isp            the ISP
     * @param bool   $expectedResult the expected result
     *
     * @return void
     */
    public function isJohnLewisVispCustomerReturnsCorrectResults($isp, $expectedResult)
    {
        $coreService = $this
            ->getMockBuilder('Core_Service')
            ->disableOriginalConstructor()
            ->setMethods(['getIsp'])
            ->getMock();
        $coreService->method('getIsp')->willReturn($isp);

        $this->assertEquals($expectedResult, $coreService->isJohnLewisVispCustomer());
    }

    /**
     * @return array
     */
    public function isJohnLewisVispCustomerReturnsCorrectResultsDataProvider()
    {
        return [
            ['johnlewis', true],
            ['greenbee', true],
            ['waitrose', true],
            ['plusnet', false],
        ];
    }
}
