<?php
/**
 * Test script for PHP 8.2 compatibility fixes
 * 
 * This script tests the JSON encoding/decoding compatibility between PHP 8.2 and PHP 5.6
 */

// Include the necessary files
include_once '/local/codebase2005/modules/FrameworkWebApi/library/SerialiserBase.php';
include_once '/local/codebase2005/modules/FrameworkWebApi/library/EncodingHelper.php';

echo "PHP 8.2 Compatibility Test for DirectDebitVerification\n";
echo "====================================================\n";
echo "PHP Version: " . PHP_VERSION . "\n\n";

// Test 1: Basic JSON encoding with problematic characters
echo "Test 1: Basic JSON encoding with problematic characters\n";
echo "-------------------------------------------------------\n";

$testData = [
    'name' => 'Test User',
    'description' => 'Test with special chars: "smart quotes" and –dashes—',
    'unicode' => 'Unicode: € £ © ® ™',
    'control' => "Control chars: \x01\x02\x03",
    'replacement' => "Replacement char: \xEF\xBF\xBD"
];

echo "Original data:\n";
var_dump($testData);

// Test standard json_encode
$standardJson = json_encode($testData);
echo "\nStandard json_encode result:\n";
echo $standardJson . "\n";

// Test PHP 5.6 compatible encoding
$compatibleJson = SerialiserBase::php56CompatibleJsonEncode($testData);
echo "\nPHP 5.6 compatible json_encode result:\n";
echo $compatibleJson . "\n";

// Test 2: Serialization and deserialization
echo "\n\nTest 2: Serialization and deserialization\n";
echo "----------------------------------------\n";

// Create a test object
$testObject = new stdClass();
$testObject->validationHash = 'abc123def456';
$testObject->accountId = 12345;
$testObject->status = 'success';
$testObject->data = $testData;

echo "Test object:\n";
var_dump($testObject);

// Serialize and encode
$serialized = serialize($testObject);
$encoded = base64_encode($serialized);

echo "\nSerialized and base64 encoded:\n";
echo substr($encoded, 0, 100) . "...\n";

// Test the getEncodedObjectAsJson method
$jsonResult = SerialiserBase::getEncodedObjectAsJson($encoded);
echo "\ngetEncodedObjectAsJson result:\n";
echo $jsonResult . "\n";

// Test 3: JSON decoding with EncodingHelper
echo "\n\nTest 3: JSON decoding with EncodingHelper\n";
echo "---------------------------------------\n";

// Create a problematic JSON string (simulating what might come from PHP 5.6)
$problematicJson = '{"test":"value with \uFFFD replacement char"}';
echo "Problematic JSON: " . $problematicJson . "\n";

// Test standard json_decode
$standardDecode = json_decode($problematicJson, true);
echo "Standard json_decode result:\n";
var_dump($standardDecode);
echo "JSON error: " . json_last_error_msg() . "\n";

// Test safe json_decode
if (class_exists('EncodingHelper')) {
    $safeDecode = EncodingHelper::safeJsonDecode($problematicJson);
    echo "\nSafe json_decode result:\n";
    var_dump($safeDecode);
} else {
    echo "\nEncodingHelper class not found\n";
}

// Test 4: Character cleaning
echo "\n\nTest 4: Character cleaning\n";
echo "------------------------\n";

$dirtyString = "Dirty string with \x01\x02\x03 control chars and \xEF\xBF\xBD replacement";
echo "Dirty string: " . bin2hex($dirtyString) . "\n";

$cleanString = EncodingHelper::cleanRawInput($dirtyString);
echo "Clean string: " . bin2hex($cleanString) . "\n";
echo "Clean string readable: " . $cleanString . "\n";

echo "\n\nTest completed!\n";
echo "If all tests pass without fatal errors, the compatibility fixes are working.\n";
