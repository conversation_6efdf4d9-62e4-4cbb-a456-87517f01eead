<?php
namespace Plusnet\DirectDebitVerification\Model;

/**
 * DirectDebitVerificationAdditionalData
 *
 * Structured data helper class to hold extra parameters to pass to DirectDebitVerificationWebFormEntity that are
 * not otherwise passed in via the DirectDebitVerificationRequestData object
 *
 * @package DirectDebitVerification
 * <AUTHOR> <PERSON> <<EMAIL>>
 */
class DirectDebitVerificationAdditionalData
{
    /**
     * Validation hash for stored DirectDebitVerificationRequestData
     *
     * @var string
     */
    public $validationHash = null;

    /**
     * The URL of the page to display to the user after the 3rd party Web Form has been submitted,
     * whilst waiting for the POSTed callback with the validation results
     *
     * @var string
     */
    public $redirectUrl = null;

    /**
     * The URL that the 3rd party provider will POST the results of the validation to once their validation
     * form has been completed
     *
     * @var string
     */
    public $callbackUrl = null;

    /**
     * Holds custom data to pass into Web Forms (max length 1024 characters)
     *
     * @var mixed
     */
    public $customData = null;

    /**
     * Constructor
     *
     * @param string $validationHash  Validation hash of DirectDebitVerificationRequestData object
     * @param array  $extraParameters Array of values
     */
    public function __construct($validationHash, array $extraParameters = array())
    {
        foreach ($extraParameters as $property => $value) {
            if (property_exists($this, $property)) {
                $this->$property = $value;
            }
        }
        $this->validationHash = $validationHash;
    }
}
