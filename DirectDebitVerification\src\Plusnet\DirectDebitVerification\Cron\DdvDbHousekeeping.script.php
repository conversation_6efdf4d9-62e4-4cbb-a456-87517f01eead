<?php
/**
 * Db Housekeeping script
 *
 * Runs Direct Debit Verification housekeeping cleanup script to remove expired Direct Debit Verification data from
 * the database.
 *
 * Example usage from working directory containing this script:
 *
 *          php DdvDbHousekeeping.script.php --batchSize 500
 *
 * @package    DirectDebitVerification
 * @subpackage Cron
 * <AUTHOR> <<EMAIL>>
 */

$moduleRoot = '/local/codebase2005/modules/';
require_once $moduleRoot . 'Framework/Libraries/bootstrap.inc.php';
require_once $moduleRoot . 'DirectDebitVerification/src/Plusnet/DirectDebitVerification/Cron/DatabaseHousekeeping.php';

use Plusnet\DirectDebitVerification\Cron\DatabaseHousekeeping;

$longOptions = [
    'batchSize:'
];

$batchSize = 1000; // Default batch size if not requested
$dbHousekeeping = new DatabaseHousekeeping();
$options = getopt('', $longOptions);

// Allow injection of test doubles for non-CLI testing
if (isset($ddvHseKeepOverride)) {
    $dbHousekeeping = $ddvHseKeepOverride['dbHousekeepingMock'];
    if (!is_null($ddvHseKeepOverride['batchSize'])) {
        $options['batchSize'] = $ddvHseKeepOverride['batchSize'];
    }
}

if (key_exists('batchSize', $options)) {
    $batchSize = $options['batchSize'];
}

$dbHousekeeping->run($batchSize);
