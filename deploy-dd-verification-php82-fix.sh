#!/bin/bash

# Deployment script for DD-Verification-Service PHP 8.2 compatibility fix
# This fixes JSON encoding issues between PHP 8.2 (dd-verification-service) and PHP 5.6 (FrameworkWebApi)

echo "=================================================================="
echo "DD-Verification-Service PHP 8.2 Compatibility Fix Deployment"
echo "=================================================================="
echo "This script deploys fixes to resolve JSON encoding issues"
echo "between PHP 8.2 (dd-verification-service) and PHP 5.6 (FrameworkWebApi)"
echo ""

# Configuration - Update these to match your environment
CONTAINER_NAME="dd-verification-service"  # Update this to your actual container name
MODULE_PATH="/local/codebase2005/modules/DirectDebitVerification"

# Function to check if container exists
check_container() {
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        echo "ERROR: Container '$CONTAINER_NAME' not found or not running"
        echo "Please update CONTAINER_NAME in this script to match your actual container name"
        echo ""
        echo "Available containers:"
        docker ps --format "table {{.Names}}\t{{.Status}}"
        return 1
    fi
    return 0
}

# Function to create backup
create_backup() {
    echo "Creating backup of original files..."
    BACKUP_DIR="$MODULE_PATH/backup-$(date +%Y%m%d-%H%M%S)"
    docker exec -it "$CONTAINER_NAME" bash -c "
        mkdir -p $BACKUP_DIR
        cp $MODULE_PATH/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php $BACKUP_DIR/DirectDebitData.php.backup 2>/dev/null || echo 'Original file not found - this is expected for new deployments'
    "
    echo "✓ Backup created in $BACKUP_DIR"
}

# Function to deploy files
deploy_files() {
    echo "Deploying enhanced DirectDebitData.php..."
    docker cp "DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php" \
        "$CONTAINER_NAME:$MODULE_PATH/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php"
    
    if [ $? -ne 0 ]; then
        echo "✗ Failed to deploy DirectDebitData.php"
        return 1
    fi
    echo "✓ DirectDebitData.php deployed successfully"
    
    echo "Deploying JsonCompatibilityHelper.php..."
    docker exec -it "$CONTAINER_NAME" mkdir -p "$MODULE_PATH/src/Plusnet/DirectDebitVerification/Helpers"
    docker cp "DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php" \
        "$CONTAINER_NAME:$MODULE_PATH/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php"
    
    if [ $? -ne 0 ]; then
        echo "✗ Failed to deploy JsonCompatibilityHelper.php"
        return 1
    fi
    echo "✓ JsonCompatibilityHelper.php deployed successfully"
    
    echo "Deploying test script..."
    docker cp "DirectDebitVerification/test-json-compatibility.php" \
        "$CONTAINER_NAME:$MODULE_PATH/test-json-compatibility.php"
    
    if [ $? -ne 0 ]; then
        echo "✗ Failed to deploy test script"
        return 1
    fi
    echo "✓ Test script deployed successfully"
    
    return 0
}

# Function to test the fix
test_fix() {
    echo "Testing the fix..."
    echo "Running JSON compatibility test..."
    docker exec -it "$CONTAINER_NAME" php "$MODULE_PATH/test-json-compatibility.php"
    
    if [ $? -eq 0 ]; then
        echo "✓ Test completed - check output above for any ERROR messages"
        return 0
    else
        echo "✗ Test failed - there may be syntax errors or missing dependencies"
        echo "Check the error output above"
        return 1
    fi
}

# Function to restart Apache
restart_apache() {
    echo "Restarting Apache gracefully..."
    docker exec -it "$CONTAINER_NAME" kill -USR1 1
    
    if [ $? -eq 0 ]; then
        echo "✓ Apache restarted gracefully"
        return 0
    else
        echo "✗ Failed to restart Apache gracefully"
        echo "You may need to restart the container manually"
        return 1
    fi
}

# Function to enable debug logging
enable_debug_logging() {
    read -p "Do you want to enable debug logging for JSON processing? (y/n): " enable_debug
    
    if [[ $enable_debug =~ ^[Yy]$ ]]; then
        docker exec -it "$CONTAINER_NAME" bash -c 'echo "export DDV_JSON_DEBUG=1" >> /etc/environment'
        echo "✓ Debug logging enabled"
        echo "  JSON processing will now log detailed information"
        echo "  Monitor logs with: docker logs -f $CONTAINER_NAME | grep DDV-JSON"
        return 0
    else
        echo "Debug logging not enabled"
        return 0
    fi
}

# Function to show monitoring commands
show_monitoring_commands() {
    echo ""
    echo "=================================================================="
    echo "Deployment Complete!"
    echo "=================================================================="
    echo ""
    echo "Next Steps:"
    echo "1. Test a DirectDebit verification request through the normal flow"
    echo "2. Monitor logs for the absence of 'Unexpected character' errors"
    echo "3. Check that DirectDebit verification completes successfully"
    echo ""
    echo "Monitoring Commands:"
    echo "  # Monitor all logs:"
    echo "  docker logs -f $CONTAINER_NAME"
    echo ""
    echo "  # Monitor for JSON errors:"
    echo "  docker logs -f $CONTAINER_NAME | grep -i 'json\\|unexpected\\|character'"
    echo ""
    echo "  # Monitor for success:"
    echo "  docker logs -f $CONTAINER_NAME | grep -i 'success\\|stored.*database'"
    echo ""
    echo "  # Monitor JSON processing (if debug enabled):"
    echo "  docker logs -f $CONTAINER_NAME | grep DDV-JSON"
    echo ""
    echo "Rollback (if needed):"
    echo "  docker exec -it $CONTAINER_NAME cp $MODULE_PATH/backup-*/DirectDebitData.php.backup $MODULE_PATH/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php"
    echo "  docker exec -it $CONTAINER_NAME kill -USR1 1"
    echo ""
    echo "=================================================================="
}

# Main execution
main() {
    echo "Step 1: Checking if container exists..."
    if ! check_container; then
        exit 1
    fi
    echo ""
    
    echo "Step 2: Creating backup..."
    create_backup
    echo ""
    
    echo "Step 3: Deploying files..."
    if ! deploy_files; then
        exit 1
    fi
    echo ""
    
    echo "Step 4: Testing the fix..."
    if ! test_fix; then
        echo "WARNING: Test failed, but continuing with deployment"
    fi
    echo ""
    
    echo "Step 5: Restarting Apache..."
    if ! restart_apache; then
        echo "WARNING: Apache restart failed, but continuing"
    fi
    echo ""
    
    echo "Step 6: Optional debug logging..."
    enable_debug_logging
    echo ""
    
    show_monitoring_commands
}

# Run main function
main
