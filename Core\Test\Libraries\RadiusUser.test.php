<?php
/**
 * Core Radius User Test
 *
 * @category  Core
 * @package   Core_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      Projects/Tr069/PDD
 * @since     File available since 2009-04-23
 */
/**
 * Core Radius User Test
 *
 * @category  Core
 * @package   Core_Test
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2008 PlusNet
 * @link      Projects/Tr069/PDD
 */
class Core_RadiusUser_Test extends PHPUnit_Framework_TestCase
{
	/**
	 * Fixture for the Core_RadiusUser
	 *
	 * @var Core_RadiusUser
	 */
	private $user;

	/**
	 * Ficture for username
	 *
	 * @var Val_Username
	 */
	private $username;

	/**
	 * PUPUnit setup function
	 *
	 * @return void
	 */
	public function setUp()
	{
		$radiusData = array(
			'intRadiusId' => 123,
			'intServiceId' => 23,
			'strUsername' => 'us3rname',
			'strEncryptedPassword' => 'EncryptedPassword'
		);

		$realmData = 'plusdsl.net';

		$db = $this->getMock('Db_Adaptor',
		                     array('getRadiusUserByRadiusId', 'getRadiusUserRealm'),
		                     array('Core', Db_Manager::DEFAULT_TRANSACTION, false));

		$db->expects($this->any())
		   ->method('getRadiusUserByRadiusId')
		   ->will($this->returnValue($radiusData));

		$db->expects($this->any())
		   ->method('getRadiusUserRealm')
		   ->will($this->returnValue($realmData));

		Db_Manager::setAdaptor('Core', $db);

		$valDb = $this->getMock('Db_Adaptor',
		                        array('getBannedUsernameMatches'),
		                        array('Val', Db_Manager::DEFAULT_TRANSACTION, false));

		$valDb->expects($this->any())
		      ->method('getBannedUsernameMatches')
		     ->will($this->returnValue(0));

		Db_Manager::setAdaptor('Val', $valDb);

		$this->user = Core_RadiusUser::getRadiusUserByRadiusId(123);

		$this->username = new Val_Username('us3rname');
	}

	/**
	 * PHPUnit tearDown functionality
	 *
	 * @return void
	 */
	public function tearDown()
	{
		Db_Manager::restoreAdaptor('Core', Db_Manager::DEFAULT_TRANSACTION);
		Db_Manager::restoreAdaptor('Val', Db_Manager::DEFAULT_TRANSACTION);
	}

	/**
	 * Test that the factory method returns the radius user object
	 *
	 * @covers Core_RadiusUser::getRadiusUserByServiceId
	 *
	 * @return void
	 */
	public function testGetRadiusUserByServiceIdCallsTheDatabaseAndReturnsARadiusUserObject()
	{
		$serviceData = array(
			'username' => 'us3rname',
			'isp'      => 'plusnet'
		);

		$radiusData = array(
			'intRadiusId' => 123,
			'intServiceId' => 23,
			'strUsername' => 'us3rname',
			'strEncryptedPassword' => 'EncryptedPassword'
		);

		$realmData = 'plusdsl.net';

		$db = $this->getMock('Db_Adaptor',
		                     array('getServiceDao', 'getRadiusUserByUsernameIsp', 'getRadiusUserRealm'),
		                     array('Core', Db_Manager::DEFAULT_TRANSACTION, false));

		$db->expects($this->once())
		   ->method('getServiceDao')
		   ->will($this->returnValue($serviceData));

		$db->expects($this->once())
		   ->method('getRadiusUserByUsernameIsp')
		   ->will($this->returnValue($radiusData));

		$db->expects($this->once())
		   ->method('getRadiusUserRealm')
		   ->will($this->returnValue($realmData));

		Db_Manager::setAdaptor('Core', $db);

		$valDb = $this->getMock('Db_Adaptor',
		                        array('getBannedUsernameMatches'),
		                        array('Val', Db_Manager::DEFAULT_TRANSACTION, false));

		$valDb->expects($this->any())
		      ->method('getBannedUsernameMatches')
		     ->will($this->returnValue(0));

		Db_Manager::setAdaptor('Val', $valDb);

		$this->user = Core_RadiusUser::getRadiusUserByServiceId(123);
	}

	/**
	 * Test that the factory method returns the radius user object
	 *
	 * @covers Core_RadiusUser::getRadiusUserByRadiusId
	 *
	 * @return void
	 */
	public function testGetRadiusUserByRadiusIdCallsTheDatabaseAndReturnsARadiusUserObject()
	{
		$radiusData = array(
			'intRadiusId' => 123,
			'intServiceId' => 23,
			'strUsername' => 'us3rname',
			'strEncryptedPassword' => 'EncryptedPassword'
		);

		$realmData = 'plusdsl.net';

		$db = $this->getMock('Db_Adaptor',
		                     array('getRadiusUserByRadiusId', 'getRadiusUserRealm'),
		                     array('Core', Db_Manager::DEFAULT_TRANSACTION, false));

		$db->expects($this->once())
		   ->method('getRadiusUserByRadiusId')
		   ->will($this->returnValue($radiusData));

		$db->expects($this->once())
		   ->method('getRadiusUserRealm')
		   ->will($this->returnValue($realmData));

		Db_Manager::setAdaptor('Core', $db);

		$valDb = $this->getMock('Db_Adaptor',
		                        array('getBannedUsernameMatches'),
		                        array('Val', Db_Manager::DEFAULT_TRANSACTION, false));

		$valDb->expects($this->any())
		      ->method('getBannedUsernameMatches')
		     ->will($this->returnValue(0));

		Db_Manager::setAdaptor('Val', $valDb);

		$this->user = Core_RadiusUser::getRadiusUserByRadiusId(123);
	}

	/**
	 * Test the getter for radius id
	 *
	 * @covers Core_RadiusUser::getRadiusId
	 *
	 * @return void
	 */
	public function testGetRadiusIdReturnsCorrectAttribute()
	{
		$expected = 123;

		$actual = $this->user->getRadiusId();

		$this->assertEquals($expected, $actual);
	}

	/**
	 * Test the getter for the username
	 *
	 *
	 * @return void
	 */
	public function testGetUsernameReturnsCorrectAttribute()
	{
		$actual = $this->user->getUsername();

		$this->assertEquals($this->username, $actual);
	}

	/**
	 * Test the getter for the encrypted password
	 *
	 * @covers Core_RadiusUser::getEncryptedPassword
	 *
	 * @return void
	 */
	public function testGetEncryptedPasswordReturnsCorrectAttribute()
	{
		$expected = 'EncryptedPassword';

		$actual = $this->user->getEncryptedPassword();

		$this->assertEquals($expected, $actual);
	}

	/**
	 * Test the getter fpr the realm
	 *
	 * @covers Core_RadiusUser::getRealm
	 *
	 * @return void
	 */
	public function testGetRealmReturnsCorrectAttribute()
	{
		$expected = 'plusdsl.net';

		$actual = $this->user->getRealm();

		$this->assertEquals($expected, $actual);
	}

	/**
	 * Test that when radius information is available, we use it to generate
	 * a Core_RadiusUser object
	 *
	 * @covers Core_RadiusUser::inferRadiusUserByServiceId
	 *
	 * @return void
	 */
	public function testInferRadiusUserByServiceIdReturnsRadiusDataWhenPossible()
	{
		$serviceData = array(
			'username' => 'us3rname',
			'isp'      => 'plusnet',
			'password' => 'servicesPassword'
		);

		$radiusData = array(
			'intRadiusId' => 123,
			'intServiceId' => 23,
			'strUsername' => 'us3rnames',
			'strEncryptedPassword' => 'EncryptedPassword'
		);

		$realmData = 'plusdsl.net';

		$db = $this->getMock('Db_Adaptor',
		                     array('getServiceDao', 'getRadiusUserByUsernameIsp', 'getRadiusUserRealm'),
		                     array('Core', Db_Manager::DEFAULT_TRANSACTION, false));

		$db->expects($this->once())
		   ->method('getServiceDao')
		   ->will($this->returnValue($serviceData));

		$db->expects($this->once())
		   ->method('getRadiusUserByUsernameIsp')
		   ->will($this->returnValue($radiusData));

		$db->expects($this->once())
		   ->method('getRadiusUserRealm')
		   ->will($this->returnValue($realmData));

		Db_Manager::setAdaptor('Core', $db);

		$valDb = $this->getMock('Db_Adaptor',
		                        array('getBannedUsernameMatches'),
		                        array('Val', Db_Manager::DEFAULT_TRANSACTION, false));

		$valDb->expects($this->exactly(2))
		      ->method('getBannedUsernameMatches')
		     ->will($this->returnValue(0));

		Db_Manager::setAdaptor('Val', $valDb);

		$this->user = Core_RadiusUser::inferRadiusUserByServiceId(123);

		$this->assertEquals($radiusData['intRadiusId'], $this->user->getRadiusId());
		$this->assertEquals($realmData, $this->user->getRealm());
		$this->assertEquals(new Val_Username($radiusData['strUsername']), $this->user->getUsername());
		$this->assertEquals($radiusData['strEncryptedPassword'], $this->user->getEncryptedPassword());
	}

	/**
	 * Test that when radius information is not available, we use the data
	 * in userdata.services to gengerate (infer) Core_RadiusUser object
	 *
	 * @covers Core_RadiusUser::inferRadiusUserByServiceId
	 *
	 * @return void
	 */
	public function testInferRadiusUserByServiceIdReturnsUserdataInformationWhenRadiusDataIsNotPossible()
	{
		$serviceData = array(
			'username' => 'us3rname',
			'isp'      => 'plusnet',
			'password' => 'password'
		);

		$radiusData = null;

		$realmData = 'plusdsl.net';

		$db = $this->getMock('Db_Adaptor',
		                     array('getServiceDao', 'getRadiusUserByUsernameIsp', 'getRadiusUserRealm'),
		                     array('Core', Db_Manager::DEFAULT_TRANSACTION, false));

		$db->expects($this->once())
		   ->method('getServiceDao')
		   ->will($this->returnValue($serviceData));

		$db->expects($this->once())
		   ->method('getRadiusUserByUsernameIsp')
		   ->will($this->returnValue($radiusData));

		$db->expects($this->once())
		   ->method('getRadiusUserRealm')
		   ->will($this->returnValue($realmData));

		Db_Manager::setAdaptor('Core', $db);

		$valDb = $this->getMock('Db_Adaptor',
		                        array('getBannedUsernameMatches'),
		                        array('Val', Db_Manager::DEFAULT_TRANSACTION, false));

		$valDb->expects($this->exactly(2))
		      ->method('getBannedUsernameMatches')
		     ->will($this->returnValue(0));

		Db_Manager::setAdaptor('Val', $valDb);

		$this->user = Core_RadiusUser::inferRadiusUserByServiceId(123);

		$this->assertEquals(-1, $this->user->getRadiusId());
		$this->assertEquals($realmData, $this->user->getRealm());
		$this->assertEquals(new Val_Username($serviceData['username']), $this->user->getUsername());
		$this->assertEquals($serviceData['password'], $this->user->getEncryptedPassword());
	}
}
