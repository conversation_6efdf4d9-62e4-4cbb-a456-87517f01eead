#!/bin/bash

# Deployment script for PHP 8.2 compatibility fix to dd-verification-service LIVE container
# This script deploys the JSON compatibility fixes to resolve Unicode character issues

echo "==================================================================="
echo "DD-Verification-Service PHP 8.2 Compatibility Fix Deployment"
echo "==================================================================="
echo "This script will deploy fixes to resolve JSON encoding issues"
echo "between PHP 8.2 (dd-verification-service) and PHP 5.6 (FrameworkWebApi)"
echo ""

# Configuration
CONTAINER_NAME="dd-verification-service"  # Update this to your actual container name
MODULE_PATH="/local/codebase2005/modules/DirectDebitVerification"

echo "Step 1: Checking if container exists..."
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "ERROR: Container '$CONTAINER_NAME' not found or not running"
    echo "Please update CONTAINER_NAME in this script to match your actual container name"
    echo "Available containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}"
    exit 1
fi

echo "✓ Container '$CONTAINER_NAME' found and running"
echo ""

echo "Step 2: Creating backup of original files..."
docker exec -it "$CONTAINER_NAME" bash -c "
    mkdir -p $MODULE_PATH/backup-$(date +%Y%m%d-%H%M%S)
    cp $MODULE_PATH/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php $MODULE_PATH/backup-$(date +%Y%m%d-%H%M%S)/DirectDebitData.php.backup 2>/dev/null || echo 'Original file not found - this is expected for new deployments'
"

echo "✓ Backup created"
echo ""

echo "Step 3: Deploying enhanced DirectDebitData.php..."
docker cp "DirectDebitVerification/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php" \
    "$CONTAINER_NAME:$MODULE_PATH/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php"

if [ $? -eq 0 ]; then
    echo "✓ DirectDebitData.php deployed successfully"
else
    echo "✗ Failed to deploy DirectDebitData.php"
    exit 1
fi

echo ""

echo "Step 4: Deploying JsonCompatibilityHelper.php..."
docker exec -it "$CONTAINER_NAME" mkdir -p "$MODULE_PATH/src/Plusnet/DirectDebitVerification/Helpers"
docker cp "DirectDebitVerification/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php" \
    "$CONTAINER_NAME:$MODULE_PATH/src/Plusnet/DirectDebitVerification/Helpers/JsonCompatibilityHelper.php"

if [ $? -eq 0 ]; then
    echo "✓ JsonCompatibilityHelper.php deployed successfully"
else
    echo "✗ Failed to deploy JsonCompatibilityHelper.php"
    exit 1
fi

echo ""

echo "Step 5: Deploying test script..."
docker cp "DirectDebitVerification/test-json-compatibility.php" \
    "$CONTAINER_NAME:$MODULE_PATH/test-json-compatibility.php"

if [ $? -eq 0 ]; then
    echo "✓ Test script deployed successfully"
else
    echo "✗ Failed to deploy test script"
    exit 1
fi

echo ""

echo "Step 6: Testing the fix..."
echo "Running JSON compatibility test..."
docker exec -it "$CONTAINER_NAME" php "$MODULE_PATH/test-json-compatibility.php"

if [ $? -eq 0 ]; then
    echo "✓ Test completed - check output above for any ERROR messages"
else
    echo "✗ Test failed - there may be syntax errors or missing dependencies"
    echo "Check the error output above"
fi

echo ""

echo "Step 7: Restarting Apache gracefully..."
docker exec -it "$CONTAINER_NAME" kill -USR1 1

if [ $? -eq 0 ]; then
    echo "✓ Apache restarted gracefully"
else
    echo "✗ Failed to restart Apache gracefully"
    echo "You may need to restart the container manually"
fi

echo ""

echo "Step 8: Enabling debug logging (optional)..."
read -p "Do you want to enable debug logging for JSON processing? (y/n): " enable_debug

if [[ $enable_debug =~ ^[Yy]$ ]]; then
    docker exec -it "$CONTAINER_NAME" bash -c 'echo "export DDV_JSON_DEBUG=1" >> /etc/environment'
    echo "✓ Debug logging enabled"
    echo "  JSON processing will now log detailed information"
    echo "  Monitor logs with: docker logs -f $CONTAINER_NAME | grep DDV-JSON"
else
    echo "Debug logging not enabled"
fi

echo ""

echo "==================================================================="
echo "Deployment Complete!"
echo "==================================================================="
echo ""
echo "Next Steps:"
echo "1. Test a DirectDebit verification request through the normal flow"
echo "2. Monitor logs for the absence of 'Unexpected character' errors"
echo "3. Check that DirectDebit verification completes successfully"
echo ""
echo "Monitoring Commands:"
echo "  # Monitor all logs:"
echo "  docker logs -f $CONTAINER_NAME"
echo ""
echo "  # Monitor for JSON errors:"
echo "  docker logs -f $CONTAINER_NAME | grep -i 'json\\|unexpected\\|character'"
echo ""
echo "  # Monitor for success:"
echo "  docker logs -f $CONTAINER_NAME | grep -i 'success\\|stored.*database'"
echo ""
echo "If you enabled debug logging:"
echo "  # Monitor JSON processing:"
echo "  docker logs -f $CONTAINER_NAME | grep DDV-JSON"
echo ""
echo "Rollback (if needed):"
echo "  docker exec -it $CONTAINER_NAME cp $MODULE_PATH/backup-*/DirectDebitData.php.backup $MODULE_PATH/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php"
echo "  docker exec -it $CONTAINER_NAME kill -USR1 1"
echo ""
echo "==================================================================="
