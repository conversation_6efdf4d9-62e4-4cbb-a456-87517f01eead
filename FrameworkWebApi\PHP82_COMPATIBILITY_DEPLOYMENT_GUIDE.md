# PHP 8.2 Compatibility Fix for DirectDebitVerification Service

## Problem Description

The DirectDebitVerification service running on PHP 8.2 was experiencing character encoding issues when communicating with FrameworkWebApi running on PHP 5.6. The error manifested as:

```
JsonParseException: Unexpected character ('�' (code 65533 / 0xfffd)): expected a valid value
```

This occurred because PHP 8.2's `json_encode()` function produces JSON with Unicode characters and encoding that PHP 5.6's `json_decode()` cannot properly parse.

## Root Cause

1. **Character Encoding Differences**: PHP 8.2 handles Unicode differently than PHP 5.6
2. **JSON Encoding Changes**: PHP 8.2 uses different default flags for JSON encoding
3. **Unicode Replacement Characters**: PHP 8.2 may introduce Unicode replacement characters (�) that PHP 5.6 cannot handle

## Solution Overview

The fix implements a compatibility layer in the FrameworkWebApi that:

1. **Safe JSON Decoding**: Cleans incoming JSON data before parsing
2. **PHP 5.6 Compatible JSON Encoding**: Uses conservative JSON encoding flags and character cleaning
3. **Character Sanitization**: Removes problematic Unicode and control characters
4. **Backward Compatibility**: Maintains compatibility with existing PHP 5.6 systems

## Files Modified

### 1. FrameworkWebApi/library/SerialiserBase.php
- **Modified**: `getEncodedObjectAsJson()` method to use PHP 5.6 compatible JSON encoding
- **Added**: `php56CompatibleJsonEncode()` method
- **Added**: `cleanObjectForPhp56()` method
- **Added**: `cleanJsonForPhp56()` method
- **Added**: `convertUnicodeToBasic()` method

### 2. FrameworkWebApi/application/controllers/SerialiserController.php
- **Modified**: Both `serialiseAction()` and `deserialiseAction()` methods to use safe JSON decoding
- **Added**: Include for EncodingHelper class

### 3. FrameworkWebApi/conf/php82-compatibility.ini
- **Updated**: JSON encoding options to use PHP 5.6 compatible flags
- **Added**: Additional error handling configuration

### 4. FrameworkWebApi/library/EncodingHelper.php
- **Existing**: Already contained compatibility helpers (no changes needed)

## Deployment Steps

### Step 1: Backup Current Files
```bash
# Backup the original files
cp FrameworkWebApi/library/SerialiserBase.php FrameworkWebApi/library/SerialiserBase.php.backup
cp FrameworkWebApi/application/controllers/SerialiserController.php FrameworkWebApi/application/controllers/SerialiserController.php.backup
cp FrameworkWebApi/conf/php82-compatibility.ini FrameworkWebApi/conf/php82-compatibility.ini.backup
```

### Step 2: Apply the Changes
The changes have already been applied to the files in this repository.

### Step 3: Test the Fix
```bash
# Run the compatibility test script
php FrameworkWebApi/test-php82-compatibility.php
```

### Step 4: Restart Services
```bash
# For Docker containers (preferred method to avoid downtime)
docker exec -it <dd-verification-container> /bin/bash
# Inside container:
kill -USR1 1  # Graceful reload of Apache

# Alternative: Restart the container (causes brief downtime)
docker restart <dd-verification-container>
```

### Step 5: Monitor Logs
```bash
# Monitor for any JSON parsing errors
docker logs -f <dd-verification-container> | grep -i "json\|encoding\|unexpected"

# Check pn-billing-api logs for the specific error
tail -f /path/to/pn-billing-api/logs/application.log | grep -i "unexpected response"
```

## Testing

### Manual Testing
1. **Run the test script**: `php FrameworkWebApi/test-php82-compatibility.php`
2. **Test DirectDebit verification flow**: Initiate a DirectDebit verification request through pn-billing-api
3. **Monitor logs**: Check for absence of the original error message

### Expected Results
- No more "Unexpected character ('�')" errors
- DirectDebit verification requests complete successfully
- JSON responses are properly parsed by PHP 5.6 systems

## Rollback Plan

If issues occur, rollback by restoring the backup files:

```bash
# Restore original files
cp FrameworkWebApi/library/SerialiserBase.php.backup FrameworkWebApi/library/SerialiserBase.php
cp FrameworkWebApi/application/controllers/SerialiserController.php.backup FrameworkWebApi/application/controllers/SerialiserController.php
cp FrameworkWebApi/conf/php82-compatibility.ini.backup FrameworkWebApi/conf/php82-compatibility.ini

# Restart services
docker restart <dd-verification-container>
```

## Key Features of the Fix

1. **Automatic Detection**: The fix automatically detects PHP 8.2 and applies compatibility measures
2. **Conservative Approach**: Uses only JSON encoding flags that are compatible with PHP 5.6
3. **Character Cleaning**: Removes problematic Unicode and control characters
4. **Error Logging**: Provides detailed logging for debugging
5. **Backward Compatible**: Does not break existing functionality on PHP 5.6 systems

## Monitoring

After deployment, monitor these metrics:
- Absence of JSON parsing errors in logs
- Successful DirectDebit verification completion rates
- No increase in API response times
- No new error patterns in pn-billing-api logs

## Future Considerations

This fix is a temporary solution for the PHP version compatibility issue. Consider:
1. **Long-term**: Upgrade FrameworkWebApi to a newer PHP version
2. **Alternative**: Implement a proper API versioning strategy
3. **Monitoring**: Set up alerts for JSON parsing errors to catch similar issues early
