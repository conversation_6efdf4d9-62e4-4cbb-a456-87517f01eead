<?php
/**
 * DirectDebitVerificationConfigTest
 *
 * @uses       PHPUnit_Framework_TestCase
 * @package    DirectDebitVerification
 * @subpackage Tests
 * <AUTHOR> Rollings <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Test\Model;

use org\bovigo\vfs\vfsStream;

use Plusnet\DirectDebitVerification\Exceptions\DirectDebitVerificationInvalidConfigException;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationConfig;
use Plusnet\DirectDebitVerification\Test\Model\TestConfig;
use Plusnet\DirectDebitVerification\Test\Util\FilesTestHelper;

class DirectDebitVerificationConfigTest extends \PHPUnit_Framework_TestCase
{
    /**
     * Runs after each test
     *
     * @return void
     */
    public function tearDown()
    {
        \Db_Manager::rollback();
        \Db_Manager::restoreAdaptor("Auth");

        $this->clearEnvironment();
    }

    /**
     * Tests loadConfigFromFile() with an actual file
     *
     * @return void
     */
    public function testLoadConfigFromFile()
    {
        $this->setMockAuthAdaptor();
        $ddvConfig = new DirectDebitVerificationConfig();
        $ddvConfig->loadConfigFromFile(TestConfig::LOCAL_TEST_CONFIG_FILE);
        $this->assertEquals(
            TestConfig::TEST_CONFIG_ARRAY['webFormProviderBaseURI'],
            $ddvConfig->getWebFormProviderBaseURI(),
            'Not returning expected webFormProviderBaseURI value after loading config from ' .
            'file path: ' . TestConfig::LOCAL_TEST_CONFIG_FILE
        );
    }

    /**
     * Tests LoadConfigFromFile() with a missing config file (container startup scenario)
     *
     * @return void
     */
    public function testLoadConfigFromFileGeneratesConfigWhenDoesNotExist()
    {
        $vfs = FilesTestHelper::prepareFileStructure();
        $filePath = 'vfs://root/config/test.json';

        $this->populateEnvironment();
        $this->populateDockerSecrets($vfs);

        $ddvConfig = new DirectDebitVerificationConfig();

        $this->setMockAuthAdaptor();

        $ddvConfig->setDockerSecretsPath('vfs://root/secrets');
        $ddvConfig->loadConfigFromFile($filePath);

        $this->assertEquals(
            TestConfig::TEST_CONFIG_ARRAY['webFormProviderBaseURI'],
            $ddvConfig->getWebFormProviderBaseURI(),
            'Not returning expected webFormProviderBaseURI value after loading config from mock env'
        );
    }

    /**
     * Tests LoadConfigFromFile() with no config and no environment
     *
     * @return void
     */
    public function testLoadConfigFromFileThrowsExceptionWhenEnvironmentEmpty()
    {
        $vfs = FilesTestHelper::prepareFileStructure();
        $filePath = 'vfs://root/config/test.json';

        $this->populateDockerSecrets($vfs);

        $ddvConfig = new DirectDebitVerificationConfig();

        $this->setMockAuthAdaptor();
        $this->setExpectedExceptionRegExp(
            DirectDebitVerificationInvalidConfigException::class,
            '/is empty in the config file/'
        );

        $ddvConfig->setDockerSecretsPath('vfs://root/secrets');
        $ddvConfig->loadConfigFromFile($filePath);
    }

    /**
     * Tests LoadConfigFromFile() with no config and no docker secrets
     *
     * @return void
     */
    public function testLoadConfigFromFileThrowsExceptionWhenDockerSecretsMissing()
    {
        FilesTestHelper::prepareFileStructure();

        $filePath = 'vfs://root/config/test.json';

        $ddvConfig = new DirectDebitVerificationConfig();

        $this->setMockAuthAdaptor();
        $this->setExpectedExceptionRegExp(
            DirectDebitVerificationInvalidConfigException::class,
            '/docker secret was not found at/'
        );

        $ddvConfig->setDockerSecretsPath('vfs://root/secrets');
        $ddvConfig->loadConfigFromFile($filePath);
    }

    /**
     * Tests LoadConfigFromFile() when writing the config fails
     *
     * @return void
     */
    public function testLoadConfigFromFileThrowsExceptionWhenWriteFails()
    {
        $vfs = FilesTestHelper::prepareFileStructure();

        $filePath = 'vfs://root/does-not-exist/test.json';

        $this->populateEnvironment();
        $this->populateDockerSecrets($vfs);

        $ddvConfig = new DirectDebitVerificationConfig();

        $this->setMockAuthAdaptor();
        $this->setExpectedExceptionRegExp(
            DirectDebitVerificationInvalidConfigException::class,
            '/Failed to generate config file at/'
        );

        $ddvConfig->setDockerSecretsPath('vfs://root/secrets');
        $ddvConfig->loadConfigFromFile($filePath);
    }

    /**
     * Tests LoadConfigFromFile() with invalid configs in file
     *
     * @param string      $messagePattern     The expected fragment to look for in the message
     * @param string|bool $configFileContents The simulated contents of the file
     * @return void
     *
     * @dataProvider providerForTestFetchAndDecodeConfigFromFileWithInvalidConfig
     */
    public function testFetchAndDecodeConfigFromFileWithInvalidConfig(
        $messagePattern,
        $configFileContents = TestConfig::TEST_CONFIG_ARRAY
    ) {
        $mockDdvConfig = $this->getDdvConfigMock(['getConfigFileContents']);
        $mockDdvConfig
            ->expects($this->once())
            ->method("getConfigFileContents")
            ->will($this->returnValue($configFileContents));


        $this->setMockAuthAdaptor();
        $this->setExpectedExceptionRegExp(
            DirectDebitVerificationInvalidConfigException::class,
            $messagePattern
        );
        $mockDdvConfig->loadConfigFromFile(TestConfig::LOCAL_TEST_CONFIG_FILE);
    }

    /**
     * Provider for TestFetchAndDecodeConfigFromFileWithInvalidConfig()
     *
     * @return array
     */
    public function providerForTestFetchAndDecodeConfigFromFileWithInvalidConfig()
    {
        return [
            'checkEmptyFileThrowsException'      => [
                'messagePattern'     => '/Empty config file at/',
                'configFileContents' => false
            ],
            'checkNonJSONContentThrowsException' => [
                'messagePattern'     => '/No valid JSON in the config file/',
                'configFileContents' => '<xml>XML not JSON content</xml>'
            ],
        ];
    }

    /**
     * Tests validateConfig()
     *
     * @return void
     *
     * @dataProvider providerForTestValidateConfig
     */
    public function testValidateConfig($messagePattern, array $decodedConfig)
    {
        $mockDdvConfig = $this->getDdvConfigMock(['fetchAndDecodeConfigFromFile']);
        $mockDdvConfig
            ->expects($this->once())
            ->method("fetchAndDecodeConfigFromFile")
            ->will($this->returnValue($decodedConfig));

        $this->setMockAuthAdaptor();
        if (!empty($messagePattern)) {
            $this->setExpectedExceptionRegExp(
                DirectDebitVerificationInvalidConfigException::class,
                $messagePattern
            );
        }
        $mockDdvConfig->loadConfigFromFile();
    }

    /**
     * Provider For testValidateConfig()
     *
     * @return array[]
     */
    public function providerForTestValidateConfig()
    {
        $testConfigArray = TestConfig::TEST_CONFIG_ARRAY;
        $invalidNamedConfigIdentifier = $testConfigArray;
        $invalidNamedConfigIdentifier['namedConfigIdentifier'] = 'invalidIdentifierWithNoHyphenToHyphen';

        $emptyKey = $testConfigArray;
        $emptyKey['webFormProviderBaseURI'] = '';

        $providerUriEndsWithForwardSlash = $testConfigArray;
        $providerUriEndsWithForwardSlash['webFormProviderBaseURI'] = '/';

        $noProviderProtocol = $testConfigArray;
        $noProviderProtocol['webFormProviderBaseURI'] = 'www.noHttp.com';

        $callbackUriEndsWithForwardSlash = $testConfigArray;
        $callbackUriEndsWithForwardSlash['webFormCallbackBaseURI'] = '/';

        $noCallbackProtocol = $testConfigArray;
        $noCallbackProtocol['webFormCallbackBaseURI'] = 'www.noHttp.com';

        $not32Chars = $testConfigArray;
        $not32Chars['webFormSharedSecret'] = '**********';

        $nonHex32Chars = $testConfigArray;
        $nonHex32Chars['webFormSharedSecret'] = 'abcdefghijklmnopqrstuvwxyz123456';

        $not32CharsDataKey = $testConfigArray;
        $not32CharsDataKey['dataKey'] = '**********';

        $nonHex32CharsDataKey = $testConfigArray;
        $nonHex32CharsDataKey['dataKey'] = 'abcdefghijklmnopqrstuvwxyz123456';

        $nonIntegerMaxWaitTimeSec = $testConfigArray;
        $nonIntegerMaxWaitTimeSec['maxWaitTimeSec'] = 'forty-two';

        $negativeMaxWaitTimeSec = $testConfigArray;
        $negativeMaxWaitTimeSec['maxWaitTimeSec'] = -1;

        $nonIntegerPassingNameScore = $testConfigArray;
        $nonIntegerPassingNameScore['passingNameScore'] = 'six';

        $lowPassingNameScore = $testConfigArray;
        $lowPassingNameScore['passingNameScore'] = -1;

        $highPassingAddressScore = $testConfigArray;
        $highPassingAddressScore['passingAddressScore'] = 42;

        $extraKey = $testConfigArray;
        $extraKey['extraKey'] = 'extra';

        $zeroNamePassingScore = $testConfigArray;
        $extraKey['passingNameScore'] = 0;

        $zeroAddressPassingScore = $testConfigArray;
        $extraKey['passingAddressScore'] = 0;

        return [
            'checkMissingExpectedConfigEntriesThrowsExceptions'     => [
                'messagePattern' => '/config does not contain the expected entries for key/',
                'decodedConfig'  => array(),
            ],
            'checkInvalidNamedConfigIdentifierValueThrowsException' => [
                'messagePattern' => '/between host and destination!/',
                'decodedConfig'  => $invalidNamedConfigIdentifier,
            ],
            'checkMissingKeyValueThrowsException'                   => [
                'messagePattern' => '/is empty in the config file/',
                'decodedConfig'  => $emptyKey,
            ],
            'checkTrailingForwardSlashThrowsException'              => [
                'messagePattern' => '/should not have a trailing/',
                'decodedConfig'  => $providerUriEndsWithForwardSlash,
            ],
            'checkNoHttpProtocolThrowsException'                    => [
                'messagePattern' => '/should begin with a protocol/',
                'decodedConfig'  => $noProviderProtocol,
            ],
            'checkTrailingForwardSlashOnCallbackThrowsException'    => [
                'messagePattern' => '/should not have a trailing/',
                'decodedConfig'  => $callbackUriEndsWithForwardSlash,
            ],
            'checkNoHttpProtocolOnCallbackThrowsException'          => [
                'messagePattern' => '/should begin with a protocol/',
                'decodedConfig'  => $noCallbackProtocol,
            ],
            'checkWrongLengthSecretThrowsException'                 => [
                'messagePattern' => '/should be a 32 character long string/',
                'decodedConfig'  => $not32Chars,
            ],
            'checkNonHexadecimal32CharLongSecretThrowsException'    => [
                'messagePattern' => '/should be a 32 character long string/',
                'decodedConfig'  => $nonHex32Chars,
            ],
            'checkWrongLengthDataKeyThrowsException'                => [
                'messagePattern' => '/should be a 32 character long string/',
                'decodedConfig'  => $not32CharsDataKey,
            ],
            'checkNonHexadecimal32CharLongDataKeyThrowsException'   => [
                'messagePattern' => '/should be a 32 character long string/',
                'decodedConfig'  => $nonHex32CharsDataKey,
            ],
            'checkNonIntegerMaxWaitTimeSec'                         => [
                'messagePattern' => '/should be a positive integer/',
                'decodedConfig'  => $nonIntegerMaxWaitTimeSec,
            ],
            'checkNegativeIntegerMaxWaitTimeSec'                    => [
                'messagePattern' => '/should be a positive integer/',
                'decodedConfig'  => $negativeMaxWaitTimeSec,
            ],
            'checkNonIntegerPassingNameThrowsException'             => [
                'messagePattern' => '/should be either 0 or a positive integer less than 10/',
                'decodedConfig'  => $nonIntegerPassingNameScore,
            ],
            'checkTooLowPassingNameThrowsException'                 => [
                'messagePattern' => '/should be either 0 or a positive integer less than 10/',
                'decodedConfig'  => $lowPassingNameScore,
            ],
            'checkTooHighPassingNameThrowsException'                => [
                'messagePattern' => '/should be either 0 or a positive integer less than 10/',
                'decodedConfig'  => $highPassingAddressScore,
            ],
            'checkAdditionalValueThrowsException'                   => [
                'messagePattern' => '/Unrecognized value/',
                'decodedConfig'  => $extraKey,
            ],
            'checkNamePassingScoreSetTo_0_WillNotThrowException'    => [
                'messagePattern' => '',
                'decodedConfig'  => $zeroNamePassingScore,
            ],
            'checkAddressPassingScoreSetTo_0_WillNotThrowException' => [
                'messagePattern' => '',
                'decodedConfig'  => $zeroAddressPassingScore,
            ]
        ];
    }

    /**
     * Test hashSecret() - Ensure responds with expected values
     *
     * @return void
     */
    public function testHashSecretReturnsExpectedFormat()
    {
        $key = '**********abcdef' . '**********abcdef';
        $algo = 'sha256';
        $expectedResponse = hex2bin('cc75443d8979fe5cdbdf2de55b822ec8b63bc55348c2e451b46b19f4746885ca');

        $ddvConfig = new DirectDebitVerificationConfig();
        $hash1 = $ddvConfig->hashSecret($key, $algo);

        $this->assertInternalType('string', $hash1, 'Did not return a string');
        $this->assertEquals(32, strlen($hash1), 'The returned string is not the expected length');
        $this->assertEquals($expectedResponse, $hash1, 'Response is not the expected binary string');
    }

    /**
     * Tests hashSecret() - Ensure responds with different values for different keys
     *
     * @return void
     */
    public function testHashSecretReturnsUniqueHashForEachKey()
    {
        $algo = DirectDebitVerificationConfig::SECURE_HASH_ALGORITHM;

        $ddvConfig = new DirectDebitVerificationConfig();
        $hash1 = $ddvConfig->hashSecret('key1', $algo);
        $hash2 = $ddvConfig->hashSecret('key2', $algo);
        $this->assertNotEquals($hash1, $hash2, 'Hashes are not different when given different keys');
    }

    /**
     * Tests various getXXX methods return correct info from config
     *
     * @param string $testedValue        Which value to test
     * @param string $targetGetterMethod Which getter method to test
     * @param string $expectedResponse   Expected response if different from the default entry in test JSON
     *
     * @return void
     *
     * @dataProvider providerForTestGetterMethodReturnsExpectedValue
     */
    public function testGetterMethodReturnsExpectedValue($testedValue, $targetGetterMethod, $expectedResponse = null)
    {
        $mockDdvConfig = $this->getDdvConfigMock(['fetchAndDecodeConfigFromFile']);
        $mockDdvConfig
            ->expects($this->once())
            ->method("fetchAndDecodeConfigFromFile")
            ->will($this->returnValue(TestConfig::TEST_CONFIG_ARRAY));
        $expectedValue = empty($expectedResponse) ? TestConfig::TEST_CONFIG_ARRAY[$testedValue] : $expectedResponse;
        $this->assertEquals(
            $expectedValue,
            $mockDdvConfig->$targetGetterMethod(),
            "Not returning expected $testedValue value:"
        );
    }

    /**
     * Provider for testGetterMethodReturnsExpectedValue()
     *
     * @return array
     */
    public function providerForTestGetterMethodReturnsExpectedValue()
    {
        // Digested SHA256 binary hash of 'FB62C4B3A42D42F2B0579A05796706A5'
        $binaryHashWebFormSecret = hex2bin('6f5f9a7f2bae162000405432010c754722c5406b3e8bf444fd9f4a55d06f8043');
        // Digested SHA256 binary hash of '352fd782c543063bd8db4e0890cc0c21'
        $binaryHashDataKeySecret = hex2bin('0b106095b01167a8bf479310f616a944e450f516e8044b2610996e20f9e5f48e');

        return [
            'checkReturnsFormProviderBaseURI'   => [
                'testedValue'        => 'webFormProviderBaseURI',
                'targetGetterMethod' => 'getWebFormProviderBaseURI',
            ],
            'checkReturnsFormCallbackBaseURI'   => [
                'testedValue'        => 'webFormCallbackBaseURI',
                'targetGetterMethod' => 'getWebFormCallbackBaseURI',
            ],
            "checkReturnsSharedSecret"          => [
                'testedValue'        => 'webFormSharedSecret',
                'targetGetterMethod' => 'getSharedSecretDigestHash',
                'expectedResponse'   => $binaryHashWebFormSecret
            ],
            "checkReturnsDataKey"               => [
                'testedValue'        => 'dataKey',
                'targetGetterMethod' => 'getDataKeyDigestHash',
                'expectedResponse'   => $binaryHashDataKeySecret
            ],
            "checkReturnsMaxWaitTimeSec"        => [
                'testedValue'        => 'maxWaitTimeSec',
                'targetGetterMethod' => 'getMaxWaitTimeSec',
            ],
            'checkReturnsPassingNameScore'      => [
                'testedValue'        => 'passingNameScore',
                'targetGetterMethod' => 'getPassingNameScore',
            ],
            'checkReturnsPassingAddressScore'   => [
                'testedValue'        => 'passingAddressScore',
                'targetGetterMethod' => 'getPassingAddressScore',
            ],
            'checkReturnsNamedConfigIdentifier' => [
                'testedValue'        => 'namedConfigIdentifier',
                'targetGetterMethod' => 'getNamedConfigIdentifier',
            ],
        ];
    }

    /**
     * Helper function to get ddvConfig object mock
     *
     * @param array $methodsToReplace Methods to override
     * @return DirectDebitVerificationConfig|\PHPUnit_Framework_MockObject_MockObject
     */
    private function getDdvConfigMock(array $methodsToReplace)
    {
        return $this->getMock(
            DirectDebitVerificationConfig::class,
            $methodsToReplace
        );
    }

    /**
     * Sets a mock Auth response so can log output
     *
     * @return void
     */
    private function setMockAuthAdaptor()
    {
        /** @var \Db_Adaptor | \PHPUnit_Framework_MockObject_MockObject $dbAdaptorAuthMock */
        $dbAdaptorAuthMock = $this->getMock(
            'Db_Adaptor',
            ['getScriptSessionDetails'],
            array('Auth', \Db_Manager::DEFAULT_TRANSACTION, true)
        );

        $dbAdaptorAuthMock->expects($this->any())
            ->method('getScriptSessionDetails')
            ->will($this->returnValue('U8GolZ00a5GGn+GFcu/HB/7+ch8='));

        \Db_Manager::setAdaptor('Auth', $dbAdaptorAuthMock, \Db_Manager::DEFAULT_TRANSACTION);
    }

    /**
     * Populates environment variables with test values
     *
     * @return void
     */
    private function populateEnvironment()
    {
        putenv('DDV_CONFIG_NAME=' . TestConfig::TEST_CONFIG_ARRAY['namedConfigIdentifier']);
        putenv('DDV_CONFIG_WEB_FORM_BASE_URI=' . TestConfig::TEST_CONFIG_ARRAY['webFormProviderBaseURI']);
        putenv('DDV_CONFIG_WEB_FORM_BASE_CALLBACK_URI=' . TestConfig::TEST_CONFIG_ARRAY['webFormCallbackBaseURI']);
        putenv('DDV_CONFIG_MAX_WAIT_TIME=' . TestConfig::TEST_CONFIG_ARRAY['maxWaitTimeSec']);
        putenv('DDV_CONFIG_PASSING_SCORE_FOR_NAME=' . TestConfig::TEST_CONFIG_ARRAY['passingNameScore']);
        putenv('DDV_CONFIG_PASSING_SCORE_FOR_ADDRESS=' . TestConfig::TEST_CONFIG_ARRAY['passingAddressScore']);
    }

    /**
     * Clears environment variables
     *
     * @return void
     */
    private function clearEnvironment()
    {
        putenv('DDV_CONFIG_NAME');
        putenv('DDV_CONFIG_WEB_FORM_BASE_URI');
        putenv('DDV_CONFIG_WEB_FORM_BASE_CALLBACK_URI');
        putenv('DDV_CONFIG_MAX_WAIT_TIME');
        putenv('DDV_CONFIG_PASSING_SCORE_FOR_NAME');
        putenv('DDV_CONFIG_PASSING_SCORE_FOR_ADDRESS');
    }

    /**
     * Populates Docker secrets with test values
     *
     * @param \bovigo\vfs\vfsDirectory|\org\bovigo\vfs\vfsStreamDirectory $vfs Virtual file system
     * @return void
     */
    private function populateDockerSecrets($vfs)
    {
        $webFormSharedSecret = vfsStream::newFile('DDV_CONFIG_WEB_FORM_SHARED_SECRET');
        $webFormSharedSecret->setContent(TestConfig::TEST_CONFIG_ARRAY['webFormSharedSecret']);
        $vfs->getChild('secrets')->addChild($webFormSharedSecret);

        $dataKeySecret = vfsStream::newFile('DDV_CONFIG_DATA_KEY');
        $dataKeySecret->setContent(TestConfig::TEST_CONFIG_ARRAY['dataKey']);
        $vfs->getChild('secrets')->addChild($dataKeySecret);
    }
}
