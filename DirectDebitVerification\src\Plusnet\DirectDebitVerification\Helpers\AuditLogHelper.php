<?php
/**
 * DirectDebitVerificationHelper class that interfaces with GImp to write audit logs
 *
 * @package    DirectDebitVerification
 * @subpackage DirectDebitVerificationHelper
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @copyright  2017 PlusNet plc
 * @filesource
 */

namespace Plusnet\DirectDebitVerification\Helpers;

use Plusnet\BillingApiClient\Util\Registry;

class AuditLogHelper
{
    const LOG_FILE_PATH = '/var/log/php5/billingPhpAudit.log';
    const CONTEXT = 'DirectDebitVerification';
    const ENVIRONMENT_LOG_PATH = 'DDV_LOG_PATH';
    const DEFAULT_UNMASKED_HEAD_SIZE = 2;
    const DEFAULT_UNMASKED_TAIL_SIZE = 2;
    const DEFAULT_MASK_CHARACTER = '*';
    private static $instance = null;

    private $logFilePath = self::LOG_FILE_PATH;

    /**
     * Restrict direct initialization, use AuditLogHelper::getInstance() instead
     *
     * @param string $logFilePath Override default log file path
     */
    private function __construct($logFilePath = null)
    {
        if ($logFilePath !== null) {
            $this->setLogFilePath($logFilePath);
        }
        $this->setDefaultLogHandler();
    }

    /**
     * Get log file path
     *
     * @return mixed|string
     */
    public function getLogFilePath()
    {
        return $this->logFilePath;
    }

    /**
     * Set logFilePath
     *
     * @param string $logFilePath Log file path
     *
     * @return void
     */
    public function setLogFilePath($logFilePath)
    {
        $this->logFilePath = $logFilePath;
    }

    /**
     * Get log file path, overriding if this is set on the environment
     *
     * @return string
     */
    private function getCalculatedLogFilePath()
    {
        $logFilePath = $this->getLogFilePath();

        // ENVIRONMENT_LOG_PATH is only set if this is running in a dd-verification-service container
        $containerPath = getenv(static::ENVIRONMENT_LOG_PATH);
        if ($containerPath !== false) {
            $logFilePath = $containerPath;
        }

        return $logFilePath;
    }

    /**
     * Set up the log handler to a file logger logging to the default directory
     *
     * @return void
     */
    public function setDefaultLogHandler()
    {
        $this->setLogHandler(new \Log_FileLogHandler($this->getCalculatedLogFilePath()));
    }

    /**
     * Register a log handler for this module with the central AuditLog
     *
     * @param \Log_LogHandler $logHandler Log handler
     *
     * @return void
     */
    public function setLogHandler(\Log_LogHandler $logHandler)
    {
        $logger = $this->generateLogger();
        $logger->registerLogHandler($logHandler);
        \Log_AuditLog::registerLoggerForContext($logger, self::CONTEXT);
    }

    /**
     * Generate a new logger
     *
     * @return \Log_Logger
     */
    private function generateLogger()
    {
        return new \Log_Logger();
    }

    /**
     * Inject an instance of AuditLogHelper (for testing)
     *
     * @param AuditLogHelper $instance New instance
     *
     * @return void
     */
    public static function setInstance(AuditLogHelper $instance)
    {
        self::$instance = $instance;
    }

    /**
     * Clear a previously-set instance of AuditLogHelper
     *
     * @return void
     */
    public static function clearInstance()
    {
        self::$instance = null;
    }

    /**
     * Create a new instance or return the existing one
     *
     * @param string $logFilePath Path to file to log to
     *
     * @return $instance
     */
    public static function getInstance($logFilePath = null)
    {
        if (self::$instance == null) {
            self::$instance = new AuditLogHelper($logFilePath);
        } elseif ($logFilePath !== null) {
            self::$instance->setLogFilePath($logFilePath);
        }

        return self::$instance;
    }

    /**
     * @param string $message Message
     * @param int    $level   Log level
     *
     * @return void
     */
    public function log(
        $message,
        $level = \Log_LogData::DEFAULT_LOG_LEVEL
    ) {
        $registry = $this->getRegistry();

        $correlationId = $registry->get('correlationId');
        $sessionId = $registry->get('sessionId');
        if (empty($correlationId)) {
            $correlationId = $registry->getCorrelationId();
        }
        if (empty($sessionId)) {
            $sessionId = $registry->getSessionId();
        }

        // Audit log messages must be single line (shouldn't be needed, but will catch any mistakes)
        $message = str_replace("\n", "\\n", $message);

        $logData = $this->getLogData($message);
        $logData->setLogLevel($level);
        $logData->setSessionId($sessionId);
        $logData->setMyCorrelationId($correlationId);

        \Log_AuditLog::writeStandard($logData, self::CONTEXT);
    }

    /**
     * Wrapper function to get registry object
     *
     * @return registry
     */
    public function getRegistry()
    {
        return Registry::getInstance();
    }

    /**
     * Wrapper function to get \Log_LogData object
     *
     * @param string $message Message
     *
     * @return \Log_LogData
     */
    public function getLogData($message)
    {
        return \Log_LogData::factory($message, self::CONTEXT);
    }

    /**
     * Mask passed string
     *
     * @param string $string           String to mask
     * @param int    $unmaskedHeadSize Unmasked head size
     * @param int    $unmaskedTailSize Unmasked tail size
     * @param string $maskChar         Character to mask with
     *
     * @return array|string|string[]
     */
    public function maskString(
        $string,
        $unmaskedHeadSize = self::DEFAULT_UNMASKED_HEAD_SIZE,
        $unmaskedTailSize = self::DEFAULT_UNMASKED_TAIL_SIZE,
        $maskChar = self::DEFAULT_MASK_CHARACTER
    ) {
        if (empty($string)) {
            return $string;
        }
        $size = strlen($string);
        $unmaskedHeadSize = $unmaskedHeadSize < 0 ? 0 : $unmaskedHeadSize;
        $unmaskedTailSize = $unmaskedTailSize < 0 ? 0 : $unmaskedTailSize;

        if ($unmaskedHeadSize == 0 && $unmaskedTailSize == 0) {
            return substr_replace(
                $string,
                str_repeat(
                    $maskChar,
                    $size
                ),
                0,
                $size
            );
        }

        if ($size <= $unmaskedHeadSize + $unmaskedTailSize) {
            $value = sprintf(
                '%s%s%s',
                substr($string, 0, $unmaskedHeadSize),
                str_repeat($maskChar, 3),
                substr($string, -$unmaskedTailSize)
            );
        } else {
            if ($unmaskedHeadSize == 0) {
                $value = substr_replace(
                    $string,
                    str_repeat(
                        $maskChar,
                        $size - $unmaskedTailSize
                    ),
                    0,
                    $size - $unmaskedTailSize
                );
            } elseif ($unmaskedTailSize == 0) {
                $value = substr_replace(
                    $string,
                    str_repeat(
                        $maskChar,
                        $size - $unmaskedHeadSize
                    ),
                    $size - ($size - $unmaskedHeadSize),
                    $size - $unmaskedHeadSize
                );
            } else {
                $adjustAmountWhenFromBiggerThanTail = $unmaskedHeadSize != $unmaskedTailSize
                    ? $unmaskedHeadSize - $unmaskedTailSize : 0;
                $value = substr_replace(
                    $string,
                    str_repeat(
                        $maskChar,
                        $size - ($unmaskedTailSize * 2) - $adjustAmountWhenFromBiggerThanTail
                    ),
                    $unmaskedHeadSize,
                    $size - ($unmaskedTailSize * 2) - $adjustAmountWhenFromBiggerThanTail
                );
            }
        }

        return $value;
    }

    /**
     * Mask regex in string
     *
     * @param string $string       String to mask in
     * @param array  $maskingRules Masking rules
     *
     * @return string
     */
    public function maskRegex($string, $maskingRules)
    {
        foreach ($maskingRules as $regexMaskRule) {
            if (!array_key_exists('pattern', $regexMaskRule)) {
                continue;
            }
            $toMask = [];
            preg_match($regexMaskRule['pattern'], $string, $toMask);
            if (is_array($toMask) && sizeof($toMask) == 2) {
                $string = str_replace(
                    $toMask[0],
                    str_replace(
                        $toMask[1],
                        AuditLogHelper::getInstance()->maskString(
                            $toMask[1],
                            isset($regexMaskRule['unmaskedHeadSize']) ?
                                $regexMaskRule['unmaskedHeadSize'] : self::DEFAULT_UNMASKED_HEAD_SIZE,
                            isset($regexMaskRule['unmaskedTailSize'])
                                ? $regexMaskRule['unmaskedTailSize'] : self::DEFAULT_UNMASKED_TAIL_SIZE,
                            isset($regexMaskRule['maskCharacter'])
                                ? $regexMaskRule['maskCharacter'] : self::DEFAULT_MASK_CHARACTER
                        ),
                        $toMask[0]
                    ),
                    $string
                );
            }
        }
        return $string;
    }

    /**
     * Mask array values
     *
     * @param array $arrayToMask  Array to ask
     * @param array $maskingRules Masking rules
     *
     * @return array
     */
    public function maskArray($arrayToMask, $maskingRules)
    {
        foreach ($arrayToMask as $key => $value) {
            if (is_array($value)) {
                $arrayToMask[$key] = $this->maskArray(
                    $value,
                    $maskingRules
                );
            } elseif (array_key_exists($key, $maskingRules)) {
                $arrayToMask[$key] = $this->maskString(
                    $arrayToMask[$key],
                    isset($maskingRules[$key]['unmaskedHeadSize'])
                        ? $maskingRules[$key]['unmaskedHeadSize'] : self::DEFAULT_UNMASKED_HEAD_SIZE,
                    isset($maskingRules[$key]['unmaskedTailSize'])
                        ? $maskingRules[$key]['unmaskedTailSize'] : self::DEFAULT_UNMASKED_TAIL_SIZE,
                    isset($maskingRules[$key]['maskCharacter'])
                        ? $maskingRules[$key]['maskCharacter'] : self::DEFAULT_MASK_CHARACTER
                );
            }
        }

        return $arrayToMask;
    }
}
