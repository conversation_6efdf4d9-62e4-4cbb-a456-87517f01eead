<?php
/**
 * Page
 *
 * <AUTHOR> <marcin.<PERSON><PERSON><PERSON><PERSON><PERSON>@bt.com>
 */

namespace Plusnet\DirectDebitVerification\CallbackReceiver;

use Plusnet\DirectDebitVerification\Exceptions\DirectDebitVerificationException;
use Plusnet\DirectDebitVerification\Helpers\AuditLogHelper;
use Plusnet\DirectDebitVerification\Helpers\DirectDebitVerificationHelper;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationResponseData;

class Page extends \Mvc_Page
{
    const TEMPLATE_DIRECTORY = 'callbackreceiver';

    /**
     * Our input (the post key used to create the message)
     *
     * @var array
     */
    protected $arrInputs = [
        'validationHash' => 'external:custom',
        'accountId'      => 'external:custom'
    ];

    /**
     * Custom validator for the payload
     *
     * @var array
     */
    protected $arrValidatorProducts = [
        'validationHash' => 'valValidationHashAccountIdAndReceivedData',
        'accountId'      => 'valValidationHashAccountIdAndReceivedData',
        'responseData'   => 'valValidationHashAccountIdAndReceivedData'
    ];

    /**
     * Validate passed validationHash and accountId and check if record in database exists for that pair
     *
     * @param string $validationHash Validation hash
     * @param int    $accountId      Account id
     *
     * @return array
     */
    public function valValidationHashAccountIdAndReceivedData($validationHash, $accountId)
    {
        if (empty($validationHash) || !is_string($validationHash) || strlen($validationHash) != 32) {
            throw new DirectDebitVerificationException(
                sprintf(
                    'Incorrect validationHash=%s passed!',
                    $validationHash
                )
            );
        }

        if (empty($accountId) || !is_numeric($accountId)) {
            throw new DirectDebitVerificationException(
                sprintf(
                    'Incorrect accountId=%s passed!',
                    $accountId
                )
            );
        }

        if (!$this->checkDirectDebitVerificationCallbackEntryExists($validationHash, $accountId)) {
            throw new DirectDebitVerificationException(
                sprintf(
                    'No valid entry in DirectDebit Verification Callback table fround for passed '
                    . 'validationHash=%s and accountId=%s!',
                    $validationHash,
                    $accountId
                )
            );
        }

        if (!isset($_POST['SortCode'])) {
            throw new DirectDebitVerificationException(
                sprintf(
                    'Missing SortCode in received Direct Debit Verification callback data for '
                    . 'validationHash=%s and accountId=%s!',
                    $validationHash,
                    $accountId
                )
            );
        }

        if (!isset($_POST['AccountNumber'])) {
            throw new DirectDebitVerificationException(
                sprintf(
                    'Missing AccountNumber in received Direct Debit Verification callback data for '
                    . 'validationHash=%s and accountId=%s!',
                    $validationHash,
                    $accountId
                )
            );
        }

        if (!isset($_POST['FirstName'])) {
            throw new DirectDebitVerificationException(
                sprintf(
                    'Missing FirstName in received Direct Debit Verification callback data for '
                    . 'validationHash=%s and accountId=%s!',
                    $validationHash,
                    $accountId
                )
            );
        }

        if (!isset($_POST['LastName'])) {
            throw new DirectDebitVerificationException(
                sprintf(
                    'Missing LastName in received Direct Debit Verification callback data for '
                    . 'validationHash=%s and accountId=%s!',
                    $validationHash,
                    $accountId
                )
            );
        }

        return [
            'validationHash' => $validationHash,
            'accountId'      => (int)$accountId,
            'responseData'   => $_POST
        ];
    }

    /**
     * Check Direct Debit Verification Callback (financial.tblDirectDebitVerificationCallback) entry exists
     *
     * @param string $validationHash Validation hash
     * @param int    $accountId      Account id
     *
     * @return bool
     */
    private function checkDirectDebitVerificationCallbackEntryExists($validationHash, $accountId)
    {
        $result = true;

        $ddvHelper = new DirectDebitVerificationHelper();

        $ddvCallbackData = $ddvHelper->getDirectDebitVerificationDetails($validationHash);

        if (empty($ddvCallbackData) || !is_array($ddvCallbackData) || !isset($ddvCallbackData['accountId']) ||
            $ddvCallbackData['accountId'] != $accountId
        ) {
            $result = false;
        }

        return $result;
    }

    /**
     * Callback receiver action to create DirectDebitVerificationResponseData object and store it in database
     *
     * @param string $validationHash Validation hash
     * @param int    $accountId      AccountId
     * @param array  $responseData   Response data (from $_POST)
     *
     * @return \Mvc_ActionResult
     */
    public function show($validationHash, $accountId, $responseData)
    {
        AuditLogHelper::getInstance()->log(
            sprintf(
                'Received DirectDebitVerification callback for validationHash=%s and accountId=%s with following '
                . 'data=%s',
                $validationHash,
                $accountId,
                AuditLogHelper::getInstance()->maskRegex(
                    json_encode($responseData),
                    DirectDebitVerificationResponseData::$maskingRules
                )
            )
        );

        $directDebitResponseData = DirectDebitVerificationResponseData::fromArray(
            $responseData
        );

        if ($directDebitResponseData instanceof DirectDebitVerificationResponseData) {
            $directDebitResponseData->setAccountId($accountId);
            $directDebitResponseData->setValidationHash($validationHash);
            $directDebitResponseData->saveToDatabase();
        }

        return new \Mvc_ActionResult(\Mvc_ActionResult::SUCCESS, []);
    }

    /**
     * Describe the show method above
     *
     * @param array $arrRequirements Applications requirements
     *
     * @return array
     */
    public static function descShow(array $arrRequirements)
    {
        $arrRequirements['bolSecure'] = true;
        $arrRequirements['bolAutoLoginAllowed'] = true;
        $arrRequirements['bolModifiesDb'] = true;
        $arrRequirements['bolIdempotent'] = false;

        return $arrRequirements;
    }
}
