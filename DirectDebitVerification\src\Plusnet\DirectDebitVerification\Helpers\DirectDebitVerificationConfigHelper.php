<?php

namespace Plusnet\DirectDebitVerification\Helpers;

use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationConfig;

/**
 * Singleton to manage loading Direct Debit Verification Config from file
 *
 * @package    DirectDebitVerification
 * <AUTHOR> <<EMAIL>>
 */
class DirectDebitVerificationConfigHelper
{
    /**
     * How long a cached config is valid for
     *
     * @var integer
     */
    const CACHE_TIME_SECONDS = 60;

    /**
     * The cached ddvConfig object
     *
     * @var DirectDebitVerificationConfig
     */
    protected static $ddvConfig = null;

    /**
     * Record of when this config was last updated
     *
     * @var \DateTimeImmutable
     */
    protected static $lastCached = null;

    /**
     * Record of the last used overriding filepath
     *
     * @var string
     */
    protected static $overrideConfigFilepath = '';

    /**
     * Returns a DirectDebitVerificationConfig object - Main class method
     *
     * @param string $overrideConfigFilepath Allows use of a non-standard filepath
     *
     * @return DirectDebitVerificationConfig
     */
    public static function get($overrideConfigFilepath = '')
    {
        if (is_null(static::$ddvConfig)
            || static::shouldRefreshCache($overrideConfigFilepath)
        ) {
            static::setDdvConfig($overrideConfigFilepath);
        }
        return static::$ddvConfig;
    }

    /**
     * Sets the stored object to an explicit object for dependency injection
     *
     * @param DirectDebitVerificationConfig $ddvConfigObj Object to store
     * @return void
     */
    public static function setObject(DirectDebitVerificationConfig $ddvConfigObj)
    {
        static::setDdvConfig('', $ddvConfigObj);
    }

    /**
     * Stores DirectDebitVerificationConfig object and updates stored cache details, instantiating where necessary
     *
     * @param string                        $overrideFilepath Filepath
     * @param DirectDebitVerificationConfig $ddvConfig        Object to store
     *
     * @return void
     */
    protected static function setDdvConfig(
        $overrideFilepath = '',
        DirectDebitVerificationConfig $ddvConfig = null
    ) {
        if (is_null($ddvConfig)) {
            $ddvConfig = new DirectDebitVerificationConfig();
            $ddvConfig->loadConfigFromFile($overrideFilepath);
        }
        static::$ddvConfig = $ddvConfig;
        static::$overrideConfigFilepath = $overrideFilepath;
        static::setLastCached();
    }

    /**
     * Whether should refresh cache yet for named filepath
     *
     * @param string $filepath filepath to consider
     *
     * @return bool
     */
    public static function shouldRefreshCache($filepath = '')
    {
        $lastUpdated = static::getLastCached();
        if (is_null($lastUpdated) || ($filepath !== '' && $filepath != static::$overrideConfigFilepath)) {
            return true;
        }

        $shouldRefresh = false;
        $currentTime = new \DateTimeImmutable();
        if ($currentTime > $lastUpdated->modify("+" . static::CACHE_TIME_SECONDS . "sec")) {
            $shouldRefresh = true;
        }

        return $shouldRefresh;
    }

    /**
     * Sets the time a new object was last stored
     *
     * @param \DateTimeImmutable|null $overrideDateTime Time override for dependency injection
     * @return void
     */
    public static function setLastCached(\DateTimeImmutable $overrideDateTime = null)
    {
        $cacheTime = $overrideDateTime;
        if (is_null($cacheTime)) {
            $cacheTime = new \DateTimeImmutable();
        }
        static::$lastCached = $cacheTime;
    }

    /**
     * Report when config was last updated
     *
     * @return \DateTimeImmutable|null
     */
    public static function getLastCached()
    {
        return static::$lastCached;
    }

    /**
     * Clears the stored configurations
     *
     * @return void
     */
    public static function reset()
    {
        static::$ddvConfig = null;
        static::$lastCached = null;
        static::$overrideConfigFilepath = '';
    }
}
