<?php
/**
 * Auth ScriptUser
 *
 * @package   Auth
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 PlusNet
 * @version   git
 * @since     File available since 2010-08-02
 */

/**
 * Auth ScriptUser
 *
 * Class for retrieving ScriptUser details.
 *
 * @package   Auth
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2010 PlusNet
 */
class Auth_ScriptUser_Test extends PHPUnit_Framework_TestCase
{
    /**
     * Database helper mock
     * @var \Plusnet\FailedPayment\Helpers\DatabaseHelper|\PHPUnit_Framework_MockObject_MockObject mock
     */
    private $databaseHelperMock;


    /**
     * SetUp
     *
     * @return void
     */
    public function setUp()
    {
        $this->databaseHelperMock = $this->getMockBuilder('\Db_Adaptor')
            ->setMethods(array('getScriptSessionDetails'))
            ->setConstructorArgs(
                array(
                    'Auth',
                    \Db_Manager::DEFAULT_TRANSACTION,
                    true
                )
            )->getMock();

        \Db_Manager::setAdaptor(
            'Auth',
            $this->databaseHelperMock,
            \Db_Manager::DEFAULT_TRANSACTION
        );
    }

    /**
     * Tests getting a script user for a partner that hasn't been set up
     *
     * @covers Auth_ScriptUser::getScriptUserForPartner
     *
     * @return void
     */
    public function testGetScriptUserForPartnerWithInvalidPartnerThrowsException()
    {
        $this->setExpectedException(
            'InvalidArgumentException',
            'Undefined PartnerDataset: ABCDEFG'
        );

        Auth_ScriptUser::getScriptUserForPartner('ABCDEFG');
    }

    /**
     * testGetScriptUserForPartnerWithValidDetails
     *
     * @param string $partner   Partner to try
     * @param string $sessionId Session ID for that partner script user
     *
     * @dataProvider dataProviderPartnerScriptUser
     *
     * @covers Auth_ScriptUser::getScriptUserForPartner
     *
     * @return void
     */
    public function testGetScriptUserForPartnerWithValidDetails($partner, $sessionId)
    {
        $scriptUserDetails = array(
            'sessionID'           => $sessionId,
            'partnerDataset'      => $partner,
            'username'            => Auth_ScriptUser::SESSION_MANAGER_USERNAME,
            'authenticationRealm' => Auth_ScriptUser::SESSION_MANAGER_REALM,
            'userType'            => 'PLUSNET_STAFF',
            'actorId'             => 5,
            'locale'              => null
        );

        $this->assertEquals($scriptUserDetails, Auth_ScriptUser::getScriptUserForPartner($partner));
    }

    /**
     * Tests getting a script user for a partner that hasn't been set up
     *
     * @covers Auth_ScriptUser::getScriptUser
     *
     * @return void
     */
    public function testGetScriptUserReturnsDefaultDetailsForPlusnet()
    {
        $scriptUserDetails = array(
            'sessionID'           => 'hcJmkzsbn+IQcPF2ilxK9eUdDVA=',
            'partnerDataset'      => 'PLUSNET',
            'username'            => Auth_ScriptUser::SESSION_MANAGER_USERNAME,
            'authenticationRealm' => Auth_ScriptUser::SESSION_MANAGER_REALM,
            'userType'            => 'PLUSNET_STAFF',
            'actorId'             => 5,
            'locale'              => null
        );

        $this->assertEquals($scriptUserDetails, Auth_ScriptUser::getScriptUser());
    }

    /**
     * Details for script users. These are the details that are different between each script user
     *
     * @return array
     */
    public function dataProviderPartnerScriptUser()
    {
        return array(
            array('PLUSNET',         'hcJmkzsbn+IQcPF2ilxK9eUdDVA='),
            array('REMOTE_INTERNET', '7VfTAZN+rloFDMJE9ZfQktXSPWI=')
        );
    }

    /**
     * @dataProvider dataProviderScriptSessionDetails
     *
     * @param string $sessionId  Session Id
     * @param string $scriptName Script Name - file name
     *
     * @return void
     */
    public function testGetScriptSessionDetailsForScriptUser($sessionId, $scriptName)
    {
        $expectedResults = array(
            'sessionID'           => $sessionId,
            'partnerDataset'      => 'PLUSNET',
            'username'            => Auth_ScriptUser::SESSION_MANAGER_USERNAME,
            'authenticationRealm' => Auth_ScriptUser::SESSION_MANAGER_REALM,
            'userType'            => 'PLUSNET_STAFF',
            'actorId'             => 5,
            'locale'              => null
        );

        $this->databaseHelperMock->expects($this->once())
             ->method('getScriptSessionDetails')
             ->will($this->returnValue($sessionId));

        $actualResults = Auth_ScriptUser::getScriptUser('PLUSNET', $scriptName);

        $this->assertEquals($expectedResults, $actualResults);
    }

    /**
     * Script user details.
     *
     * @return array
     */
    public function dataProviderScriptSessionDetails()
    {
        return array(
            array(
                'retCode'  => '782e0290ff9d47d17251f64d73896a8a',
                'fileName' => 'quote.php'
            ),
            array(
                'retCode'  => 'hcJmkzsbn+IQcPF2ilxK9eUdDVA=',
                'fileName' => 'noSuchFile.php'
            )
        );
    }
}
