#!/bin/bash

# Diagnostic script for DD-Verification-Service PHP 8.2 compatibility issue
# This script helps diagnose the JSON encoding issue between PHP 8.2 and PHP 5.6

echo "=================================================================="
echo "DD-Verification-Service Diagnostic Script"
echo "=================================================================="
echo "This script diagnoses the JSON encoding issue between"
echo "PHP 8.2 (dd-verification-service) and PHP 5.6 (FrameworkWebApi)"
echo ""

# Configuration - Update this to match your environment
CONTAINER_NAME="dd-verification-service"  # Update this to your actual container name

# Function to check container status
check_container_status() {
    echo "1. Container Status Check"
    echo "========================"
    
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo "✓ Container '$CONTAINER_NAME' is running"
        
        # Get container details
        echo ""
        echo "Container Details:"
        docker ps | grep "$CONTAINER_NAME" | awk '{print "  Name: " $NF "\n  Status: " $(NF-1) "\n  Ports: " $(NF-2)}'
        
        # Check PHP version in container
        echo ""
        echo "PHP Version in container:"
        docker exec -it "$CONTAINER_NAME" php -v | head -1
        
        return 0
    else
        echo "✗ Container '$CONTAINER_NAME' not found or not running"
        echo ""
        echo "Available containers:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        return 1
    fi
}

# Function to check logs
check_logs() {
    echo ""
    echo "2. Recent Logs Check"
    echo "==================="
    
    echo "Last 20 lines of container logs:"
    docker logs --tail=20 "$CONTAINER_NAME"
    
    echo ""
    echo "Checking for JSON-related errors in logs:"
    if docker logs --tail=100 "$CONTAINER_NAME" | grep -i "json\|unexpected\|character\|encoding" | head -5; then
        echo "Found JSON-related errors (showing first 5)"
    else
        echo "No JSON-related errors found in recent logs"
    fi
}

# Function to check HTTP access
check_http_access() {
    echo ""
    echo "3. HTTP Access Check"
    echo "==================="
    
    # Check if Apache is responding
    echo "Checking if Apache is responding in container..."
    if docker exec -it "$CONTAINER_NAME" curl -s -o /dev/null -w "%{http_code}" http://localhost/; then
        HTTP_CODE=$(docker exec -it "$CONTAINER_NAME" curl -s -o /dev/null -w "%{http_code}" http://localhost/)
        echo "✓ Apache responding with HTTP code: $HTTP_CODE"
    else
        echo "✗ Apache not responding in container"
    fi
    
    # Check Apache access logs
    echo ""
    echo "Recent Apache access logs:"
    docker exec -it "$CONTAINER_NAME" tail -5 /var/log/apache2/access.log 2>/dev/null || echo "Access log not found or not accessible"
    
    # Check Apache error logs
    echo ""
    echo "Recent Apache error logs:"
    docker exec -it "$CONTAINER_NAME" tail -5 /var/log/apache2/error.log 2>/dev/null || echo "Error log not found or not accessible"
}

# Function to check DirectDebitVerification module
check_ddv_module() {
    echo ""
    echo "4. DirectDebitVerification Module Check"
    echo "======================================"
    
    MODULE_PATH="/local/codebase2005/modules/DirectDebitVerification"
    
    echo "Checking if DirectDebitVerification module exists..."
    if docker exec -it "$CONTAINER_NAME" ls -la "$MODULE_PATH" >/dev/null 2>&1; then
        echo "✓ DirectDebitVerification module found"
        
        echo ""
        echo "Module structure:"
        docker exec -it "$CONTAINER_NAME" ls -la "$MODULE_PATH" | head -10
        
        echo ""
        echo "Checking DirectDebitData.php:"
        if docker exec -it "$CONTAINER_NAME" ls -la "$MODULE_PATH/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php" >/dev/null 2>&1; then
            echo "✓ DirectDebitData.php found"
            
            # Check if it has the PHP 8.2 compatibility fix
            if docker exec -it "$CONTAINER_NAME" grep -q "php56CompatibleJsonEncode" "$MODULE_PATH/src/Plusnet/DirectDebitVerification/Model/DirectDebitData.php" 2>/dev/null; then
                echo "✓ PHP 8.2 compatibility fix is already applied"
            else
                echo "✗ PHP 8.2 compatibility fix is NOT applied"
            fi
        else
            echo "✗ DirectDebitData.php not found"
        fi
    else
        echo "✗ DirectDebitVerification module not found"
    fi
}

# Function to test JSON encoding
test_json_encoding() {
    echo ""
    echo "5. JSON Encoding Test"
    echo "===================="
    
    echo "Testing JSON encoding in container..."
    
    # Create a simple test script
    TEST_SCRIPT='<?php
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Testing JSON encoding with problematic characters...\n";

$testData = [
    "normal" => "Normal text",
    "unicode" => "Unicode: € £ © ® ™",
    "quotes" => "Smart quotes: "hello" and 'world'",
    "dashes" => "Dashes: – and —",
    "control" => "Control chars: \x01\x02\x03",
    "replacement" => "Replacement: \xEF\xBF\xBD"
];

echo "Standard json_encode:\n";
$standardJson = json_encode($testData);
echo $standardJson . "\n";

echo "\nTesting decode:\n";
$decoded = json_decode($standardJson, true);
if ($decoded === null && json_last_error() !== JSON_ERROR_NONE) {
    echo "ERROR: Cannot decode JSON: " . json_last_error_msg() . "\n";
} else {
    echo "SUCCESS: JSON can be decoded\n";
}
?>'
    
    # Run the test
    echo "$TEST_SCRIPT" | docker exec -i "$CONTAINER_NAME" php
}

# Function to check environment variables
check_environment() {
    echo ""
    echo "6. Environment Variables Check"
    echo "============================="
    
    echo "Checking relevant environment variables..."
    docker exec -it "$CONTAINER_NAME" env | grep -E "(PHP|DDV|ENVIRONMENT)" | sort
}

# Function to show recommendations
show_recommendations() {
    echo ""
    echo "=================================================================="
    echo "Diagnostic Summary and Recommendations"
    echo "=================================================================="
    echo ""
    echo "Based on the diagnostic results above:"
    echo ""
    echo "If you see:"
    echo "✗ PHP 8.2 compatibility fix is NOT applied"
    echo "  → Run the deployment script: ./deploy-dd-verification-php82-fix.sh"
    echo ""
    echo "If you see JSON encoding errors:"
    echo "  → The fix needs to be applied to resolve PHP 8.2 → PHP 5.6 compatibility"
    echo ""
    echo "If container is not running:"
    echo "  → Start the container first, then run this diagnostic again"
    echo ""
    echo "If DirectDebitVerification module is not found:"
    echo "  → Check the container configuration and module paths"
    echo ""
    echo "Next steps:"
    echo "1. If fix is not applied: Run ./deploy-dd-verification-php82-fix.sh"
    echo "2. Test a DirectDebit verification request"
    echo "3. Monitor logs for the absence of Unicode character errors"
    echo ""
    echo "=================================================================="
}

# Main execution
main() {
    if ! check_container_status; then
        echo ""
        echo "Cannot proceed without a running container."
        echo "Please update CONTAINER_NAME in this script and ensure the container is running."
        exit 1
    fi
    
    check_logs
    check_http_access
    check_ddv_module
    test_json_encoding
    check_environment
    show_recommendations
}

# Run main function
main
