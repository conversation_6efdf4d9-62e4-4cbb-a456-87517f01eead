<?php
/**
 * DataEncryptionHelper class to assist with en/de-crypting Direct Debit Verification data for at-rest storage
 *
 * @package    DirectDebitVerification
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Helpers;

use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationConfig;

/**
 * EncryptionHelper class
 *
 * @package DirectDebitVerification
 * <AUTHOR> <PERSON> <<EMAIL>>
 */
class DataEncryptionHelper
{
    /**
     * @var string[] Field names which are stored encrypted in financial.tblDirectDebitVerificationCallback
     */
    const DB_ENCRYPTED_DATA_FIELDS = ['verificationRequestData', 'verificationResponseData'];

    /**
     * @var string Openssl constant for the aes-256 in Cipher Block Chaining mode  - see openssl_get_cipher_methods()
     */
    const AES_256_CBC = 'aes-256-cbc';

    private static $instance = null;

    /**
     * @var string Openssl constant for the algorithm to hash the key to the max length for the encryption cipher
     *             - should match the max keylength of the $encryptionCipher chosen
     */
    const HASHING_ALGORITHM = 'sha256';

    /**
     * @var string The cipher to use for this encoding
     */
    private $encryptionCipher = self::AES_256_CBC;

    /**
     * @var EncryptionHelper Cached EncryptionHelper object
     */
    private $encryptionHelper = null;

    /**
     * Constructor
     *
     * @return void
     */
    private function __construct()
    {
    }

    /**
     * Get instance
     *
     * @return DataEncryptionHelper
     */
    public static function getInstance()
    {
        if (empty(static::$instance)) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    /**
     * Set instance
     *
     * @param DataEncryptionHelper $instance DataEncryptionHelper instance
     *
     * @return void
     */
    public static function setInstance($instance)
    {
        static::$instance = $instance;
    }

    /**
     * Clear instance
     *
     * @return void
     */
    public static function clearInstance()
    {
        static::$instance = null;
    }

    /**
     * Decrypt a string using the provided key hash
     *
     * @param string $stringToDecrypt The string that should be decrypted
     *
     * @return string The decrypted string
     */
    public function decrypt($stringToDecrypt)
    {
        $encryptionHelper = $this->getEncryptionHelper();

        return $encryptionHelper->decrypt($stringToDecrypt, $this->getEncryptionKey());
    }

    /**
     * Encrypt a string using the provided key hash
     *
     * @param string $stringToEncrypt The string that should be encrypted
     *
     * @return string The encrypted string
     */
    public function encrypt($stringToEncrypt)
    {
        $encryptionHelper = $this->getEncryptionHelper();

        return $encryptionHelper->encrypt($stringToEncrypt, $this->getEncryptionKey());
    }

    /**
     * Getter for this class's set encryptionCipher string
     *
     * @return string encryptionCipher string to be used
     */
    public function getEncryptionCipher()
    {
        return $this->encryptionCipher;
    }

    /**
     * Getter for an EncryptionHelper class object configured using this class's encryptionCipher
     *
     * @return EncryptionHelper The generic DDV Encryption helper class
     */
    public function getEncryptionHelper()
    {
        if (is_null($this->encryptionHelper)) {
            $this->encryptionHelper = new EncryptionHelper($this->getEncryptionCipher());
        }

        return $this->encryptionHelper;
    }

    /**
     * Returns the encryption key to use
     *
     * @return string Key to use when encrypting
     */
    public function getEncryptionKey()
    {
        /** @var DirectDebitVerificationConfig $ddvConfig */
        $ddvConfig = DirectDebitVerificationConfigHelper::get();

        return $ddvConfig->getDataKeyDigestHash();
    }

    /**
     * Function to decrypt the fields in table financial.tblDirectDebitVerificationCallback that will have been
     * encrypted at rest
     *
     * @param array $ddvCallback Array representation of a single row containing fields to decrypt
     *
     * @return array Array representation of a single row with pre-encrypted fields now decrypted
     */
    public function returnDecryptedDdvCallbackData($ddvCallback)
    {
        if (is_array($ddvCallback)) {
            foreach (self::DB_ENCRYPTED_DATA_FIELDS as $key => $field) {
                $decodedField = $this->returnDecryptedDdvDataField($field, $ddvCallback);
                if (!empty($decodedField)) {
                    $ddvCallback[$field] = $decodedField;
                }
            }
        }

        return $ddvCallback;
    }

    /**
     * Helper function to decrypt a field, if valid
     *
     * @param string $field       Key / name of field to decrypt
     * @param array  $ddvCallback Array containing above $field to decrypt
     *
     * @return string Decrypted value of $ddvCallback[$field]
     */
    private function returnDecryptedDdvDataField($field, array $ddvCallback)
    {
        $value = $ddvCallback[$field];
        if (array_key_exists($field, $ddvCallback)
            && is_string($value)
            && !empty($value)
        ) {
            return $this->decrypt($ddvCallback[$field]);
        }
    }
}
