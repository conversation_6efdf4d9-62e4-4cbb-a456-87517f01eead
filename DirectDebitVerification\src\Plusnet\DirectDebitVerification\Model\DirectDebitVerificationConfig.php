<?php
/**
 * Class to assist with loading Direct Debit Verification config from file
 *
 * @package    DirectDebitVerification
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Model;

use Plusnet\DirectDebitVerification\Exceptions\DirectDebitVerificationInvalidConfigException;

/**
 * DirectDebitVerificationConfig class
 *
 * Class to load Direct Debit Verification config from a file
 *
 * If adding a new input field to this class:
 *   1) Add a new class variable to hold the new variable
 *   2) Add a new entry to the EXPECTED_CONFIG_KEYS constant
 *   3) Add the Docker env variable/secret name to fetchConfigFromEnv
 *   4) Add a new case to validateConfig() with checks to validate your variable
 *   5) Add a line to set the new class property in setConfigFromValidatedValues()
 *   6) Add a public getter method for the new class property
 *
 * @package DirectDebitVerification
 * <AUTHOR> <<EMAIL>>
 */
class DirectDebitVerificationConfig
{
    /**
     * Array keys that must exist in the config - values are ignored
     *
     * @var array
     */
    const EXPECTED_CONFIG_KEYS = [
        'namedConfigIdentifier'  => '',
        'webFormProviderBaseURI' => '',
        'webFormCallbackBaseURI' => '',
        'webFormSharedSecret'    => '',
        'dataKey'                => '',
        'maxWaitTimeSec'         => 0,
        'passingNameScore'       => 0,
        'passingAddressScore'    => 0,
    ];

    /**
     * Default path to use for the Direct Debit Verification config file
     *
     * @var string
     */
    const PATH = __DIR__ . '/../Config/directDebitVerificationConfig.json';

    /** @var string */
    const SECURE_HASH_ALGORITHM = 'sha256';

    /**
     * Path to load Docker secrets from
     *
     * @var string
     */
    private $dockerSecretsPath = '/run/secrets';

    /**
     * A name to use for the config to help more easily identify where it should be installed
     *
     * Expected format:
     *  '<expectedFileHostCategory>-to-<destination>'
     * e.g:
     *  developmentEnvironmentServer-to-localMockTestPlatform
     *  productionEnvironmentServer-to-liveBottomlinePtxPlatform
     *
     * @var string a string in the above format describing where the config is intended for and where it points
     */
    private $namedConfigIdentifier = null;

    /**
     * Base URL path to the provider/s web form page with no trailing forward slash
     *
     * @var string
     */
    private $webFormProviderBaseURI = null;

    /**
     * Base URL path for the address the provider should POST the assessed results of validating a successfully
     * submitted Web Form to, with no trailing forward slash
     *
     * @var string
     */
    private $webFormCallbackBaseURI = null;

    /**
     * Hashed digest of webForm service shared secret key
     *
     * @var string Binary string
     */
    private $sharedSecretDigestHash = null;

    /**
     * Hashed digest of ddv data key
     *
     * @var string Binary string
     */
    private $dataKeyDigestHash = null;

    /**
     * The length of time to wait for a ddv web form response after submitting
     *
     * @var integer
     */
    private $maxWaitTimeSec = null;

    /**
     * The minimum score needed to consider the name check passed
     *
     * @var integer
     */
    private $passingNameScore = null;

    /**
     * The minimum score needed to consider the address check passed
     *
     * @var integer
     */
    private $passingAddressScore = null;

    /**
     * Variable to hold disk access status
     *
     * @var bool
     */
    private $notLoadedFromFile = true;

    /**
     * Loads config from json file
     *
     * @param string $overrideConfigFilepath Allows default filepath to be overridden for unit testing
     * @return void
     *
     * @throws DirectDebitVerificationInvalidConfigException
     */
    public function loadConfigFromFile($overrideConfigFilepath = '')
    {
        $configFilepath = self::PATH;
        if (!empty($overrideConfigFilepath)) {
            $configFilepath = $overrideConfigFilepath;
        }

        $decodedConfig = $this->fetchAndDecodeConfigFromFile($configFilepath);
        $this->validateConfig($decodedConfig);
        $this->setConfigFromValidatedValues($decodedConfig);

        $this->notLoadedFromFile = false;
    }

    /**
     * Returns an array containing config loaded from json in a target file
     *
     * @param string $configFilepath The path to the config file containing JSON
     * @return array
     *
     * @throws DirectDebitVerificationInvalidConfigException
     */
    protected function fetchAndDecodeConfigFromFile($configFilepath)
    {
        if (!file_exists($configFilepath)) {
            // Attempt to generate config from environment
            $generatedConfig = $this->fetchConfigFromEnv();

            try {
                $this->writeConfigFileContents($configFilepath, json_encode($generatedConfig));
            } catch (\Exception $e) {
                throw new DirectDebitVerificationInvalidConfigException(
                    "Failed to generate config file at '$configFilepath",
                    0,
                    $e
                );
            }

            return $generatedConfig;
        }

        $configFileContents = $this->getConfigFileContents($configFilepath);
        if (empty($configFileContents)) {
            throw new DirectDebitVerificationInvalidConfigException(
                "Empty config file at '$configFilepath'"
            );
        }

        $decodedConfig = json_decode($configFileContents, true);
        if (empty($decodedConfig)) {
            throw new DirectDebitVerificationInvalidConfigException(
                "No valid JSON in the config file at '$configFilepath'"
            );
        }

        return $decodedConfig;
    }

    /**
     * Gets content from config file
     *
     * @param string $configFilepath Path to load contents from
     * @return false|string
     */
    protected function getConfigFileContents($configFilepath)
    {
        return file_get_contents($configFilepath);
    }

    /**
     * Writes content to config file
     *
     * @param string $configFilepath Path to write to
     * @param string $contents       Content to write
     * @return false|int
     */
    protected function writeConfigFileContents($configFilepath, $contents)
    {
        return file_put_contents($configFilepath, $contents);
    }

    /**
     * Loads config from the environment
     *
     * @return array
     */
    private function fetchConfigFromEnv()
    {
        $config = array();

        $config['namedConfigIdentifier'] = $this->getEnvVariable('DDV_CONFIG_NAME');
        $config['webFormProviderBaseURI'] = $this->getEnvVariable('DDV_CONFIG_WEB_FORM_BASE_URI');
        $config['webFormCallbackBaseURI'] = $this->getEnvVariable('DDV_CONFIG_WEB_FORM_BASE_CALLBACK_URI');
        $config['webFormSharedSecret'] = $this->getDockerSecret('DDV_CONFIG_WEB_FORM_SHARED_SECRET');
        $config['dataKey'] = $this->getDockerSecret('DDV_CONFIG_DATA_KEY');
        $config['maxWaitTimeSec'] = intval($this->getEnvVariable('DDV_CONFIG_MAX_WAIT_TIME'));
        $config['passingNameScore'] = intval($this->getEnvVariable('DDV_CONFIG_PASSING_SCORE_FOR_NAME'));
        $config['passingAddressScore'] = intval($this->getEnvVariable('DDV_CONFIG_PASSING_SCORE_FOR_ADDRESS'));

        return $config;
    }

    /**
     * Gets the contents of an environment variable.
     * Protected to allow for mocking.
     *
     * @param string $name Variable to load
     * @return false|string
     */
    protected function getEnvVariable($name)
    {
        return getenv($name);
    }

    /**
     * Gets the contents of an environment variable.
     * Protected to allow for mocking.
     *
     * @param string $name Variable to load
     * @return false|string
     */
    protected function getDockerSecret($name)
    {
        $secretPath = $this->dockerSecretsPath . '/' . $name;

        if (!file_exists($secretPath)) {
            throw new DirectDebitVerificationInvalidConfigException(
                "The docker secret was not found at '$secretPath'"
            );
        }

        return rtrim(file_get_contents($secretPath));
    }

    /**
     * Sets the path to read Docker secrets from.
     * Provided for testing.
     *
     * @param string $dockerSecretsPath The base path
     * @return void
     */
    public function setDockerSecretsPath($dockerSecretsPath)
    {
        $this->dockerSecretsPath = $dockerSecretsPath;
    }

    /**
     * Validates the values of the found config
     *
     * @param array $config Array of configuration values to validate
     * @return void
     *
     * @throws DirectDebitVerificationInvalidConfigException
     */
    protected function validateConfig(array $config)
    {
        $this->checkForExpectedConfigEntries($config);

        foreach ($config as $key => $value) {
            if (!is_numeric($value) && empty($value)) {
                throw new DirectDebitVerificationInvalidConfigException(
                    "Value of '$key' is empty in the config file!"
                );
            }

            switch ($key) {
                case 'namedConfigIdentifier':
                    if (!preg_match('/.*-to-.*/', $value)) {
                        throw new DirectDebitVerificationInvalidConfigException(
                            "Value of '$key' should contain the string '-to-' between host and destination!"
                        );
                    }
                    break;
                case 'webFormProviderBaseURI':
                case 'webFormCallbackBaseURI':
                    if (substr($value, -1) == '/') {
                        throw new DirectDebitVerificationInvalidConfigException(
                            "Value of '$key' should not have a trailing '/'!"
                        );
                    }
                    if (!preg_match('/^http[s]?:\/\//', $value)) {
                        throw new DirectDebitVerificationInvalidConfigException(
                            "Value of '$key' should begin with a protocol: 'https://' or 'http://'"
                        );
                    }
                    break;
                case 'webFormSharedSecret':
                case 'dataKey':
                    if (!(strlen($value) == 32 && ctype_xdigit($value))) {
                        throw new DirectDebitVerificationInvalidConfigException(
                            "Value of '$key' should be a 32 character long string of hexadecimal"
                        );
                    }
                    break;
                case 'maxWaitTimeSec':
                    if (!(is_integer($value) && $value > 0)) {
                        throw new DirectDebitVerificationInvalidConfigException(
                            "Value of '$key' should be a positive integer"
                        );
                    }
                    break;
                case 'passingNameScore':
                case 'passingAddressScore':
                    if (!(is_integer($value) && $value >= 0 && $value < 10)) {
                        throw new DirectDebitVerificationInvalidConfigException(
                            "Value of '$key' should be either 0 or a positive integer less than 10"
                        );
                    }
                    break;
                default:
                    throw new DirectDebitVerificationInvalidConfigException(
                        "Unrecognized value: '$key' found in the config file!"
                    );
            }
        }
    }

    /**
     * Checks that config is not missing any expected entries and throws exceptions if missing
     *
     * @param array $config Array of config to check for expected entries on
     * @return void
     *
     * @throws DirectDebitVerificationInvalidConfigException
     */
    protected function checkForExpectedConfigEntries($config)
    {
        $missingKeys = array_diff_key(self::EXPECTED_CONFIG_KEYS, $config);
        if (!empty($missingKeys)) {
            $missingValues = implode(", ", array_keys($missingKeys));
            throw new DirectDebitVerificationInvalidConfigException(
                "The config does not contain the expected entries for key(s): '$missingValues'"
            );
        }
    }

    /**
     * Sets config from passed pre-validation values
     *
     * @param array $decodedConfig Config to set
     * @return void
     */
    protected function setConfigFromValidatedValues($decodedConfig)
    {
        $this->namedConfigIdentifier = $decodedConfig['namedConfigIdentifier'];
        $this->webFormProviderBaseURI = $decodedConfig['webFormProviderBaseURI'];
        $this->webFormCallbackBaseURI = $decodedConfig['webFormCallbackBaseURI'];
        $this->maxWaitTimeSec = $decodedConfig['maxWaitTimeSec'];
        $this->passingAddressScore = $decodedConfig['passingAddressScore'];
        $this->passingNameScore = $decodedConfig['passingAddressScore'];

        $this->sharedSecretDigestHash = $this->hashSecret($decodedConfig['webFormSharedSecret']);
        // Note below argument should match vale of DataEncryptionHelper::HASHING_ALGORITHM
        $this->dataKeyDigestHash = $this->hashSecret($decodedConfig['dataKey'], 'sha256');
    }

    /**
     * Computes and returns a binary digest hashed value of the provided key
     *
     * @param string $key       The data: the secret key to compute a digest hash from
     * @param string $algorithm Which digest method algorithm to use
     *
     * @return string|false
     */
    public function hashSecret($key, $algorithm = self::SECURE_HASH_ALGORITHM)
    {
        return openssl_digest($key, $algorithm, true);
    }

    /**
     * Populates class from file if not already loaded
     *
     * @return void
     */
    public function ensureLoadedFromFile()
    {
        if ($this->notLoadedFromFile) {
            $this->loadConfigFromFile();
        }
    }

    /**
     * Getter for namedConfigIdentifier
     *
     * @return string the webFormProviderBaseURI
     */
    public function getNamedConfigIdentifier()
    {
        $this->ensureLoadedFromFile();
        return $this->namedConfigIdentifier;
    }

    /**
     * Getter for webFormProviderBaseURI
     *
     * @return string the webFormProviderBaseURI
     */
    public function getWebFormProviderBaseURI()
    {
        $this->ensureLoadedFromFile();
        return $this->webFormProviderBaseURI;
    }

    /**
     * Getter for webFormCallbackBaseURI
     *
     * @return string the webFormCallbackBaseURI
     */
    public function getWebFormCallbackBaseURI()
    {
        $this->ensureLoadedFromFile();
        return $this->webFormCallbackBaseURI;
    }

    /**
     * Getter for sharedSecretDigestHash
     *
     * @return string Binary string of digested shared secret key
     */
    public function getSharedSecretDigestHash()
    {
        $this->ensureLoadedFromFile();
        return $this->sharedSecretDigestHash;
    }

    /**
     * Getter for sharedSecretDigestHash
     *
     * @return string Binary string of digested shared secret key
     */
    public function getDataKeyDigestHash()
    {
        $this->ensureLoadedFromFile();
        return $this->dataKeyDigestHash;
    }

    /**
     * Getter for maxWaitTimeSec
     *
     * @return integer
     */
    public function getMaxWaitTimeSec()
    {
        $this->ensureLoadedFromFile();
        return $this->maxWaitTimeSec;
    }

    /**
     * Getter for passingNameScore
     *
     * @return integer
     */
    public function getPassingNameScore()
    {
        $this->ensureLoadedFromFile();
        return $this->passingNameScore;
    }

    /**
     * Getter for passingAddressScore
     *
     * @return integer
     */
    public function getPassingAddressScore()
    {
        $this->ensureLoadedFromFile();
        return $this->passingAddressScore;
    }
}
