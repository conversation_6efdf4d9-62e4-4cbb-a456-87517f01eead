<?php
/**
 * Class AuthTest
 *
 * @package    Framework
 * @subpackage Auth
 * <AUTHOR> <<EMAIL>>
 * @copyright  2021 PlusNet plc
 */
class Auth_PasswordReset_Test extends Plusnet_TestCase
{
    const SID_VALID = '123';
    const UUID_VALID = 'valid-uuid';
    const UUID_TIMESTAMP_VALID = '2021-01-01 12:00:00';

    const CALLED_WITH_SID_UUID = [self::SID_VALID, self::UUID_VALID];
    const CALLED_WITH_UUID_SID = [self::UUID_VALID, self::SID_VALID];

    const STATEMENT_DELETE = 'deletePasswordResetByServiceIdAndUuid';
    const STATEMENT_TIMESTAMP = 'getPasswordResetTimestampByUuidAndServiceId';
    const STATEMENT_UUID = 'getPasswordResetUuidByServiceId';
    const STATEMENT_UPDATE = 'updatePasswordResetData';
    const STATEMENT_INSERT = 'insertPasswordResetData';

    // Mocks
    private function getDbMock(...$statements)
    {
        return $this->getMock(
            'Db_Adaptor',
            $statements,
            ['Auth', Db_Manager::DEFAULT_TRANSACTION, false]
        );
    }

    private function setupDbMock(
        $dbAdaptor,
        $statement,
        $calledTimes,
        $calledWith = [],
        $throws = false,
        $returns = false
    ) {
        if (gettype($calledWith) === 'string') {
            $calledWith = [$calledWith];
        }

        if ($throws) {
            $dbAdaptor->expects($this->once())
                ->method($statement)
                ->will($this->throwException(new Exception("Something went wrong")));
            return;
        }

        $dbAdaptor->expects($calledTimes)
            ->method($statement)
            ->with(...$calledWith)
            ->will($this->returnValue($returns));
    }

    // Tests
    /**
     * Should be able to create a new instance of Auth_PasswordReset
     *
     * @covers Auth_PasswordReset
     */
    public function testShouldCreateNewInstance()
    {
        $passwordReset = new Auth_PasswordReset();
        $this->assertTrue(is_object($passwordReset));
        $this->assertTrue($passwordReset instanceof Auth_PasswordReset);
    }

    // insertPasswordResetData tests
    public function testInsertPasswordResetData()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_INSERT);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_INSERT, $this->once(), self::CALLED_WITH_SID_UUID);
        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $reset->insertPasswordResetData(self::SID_VALID, self::UUID_VALID);
    }

    public function testInsertPasswordResetDataThrowsError()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_INSERT);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_INSERT, $this->once(), self::CALLED_WITH_SID_UUID, true);
        Db_Manager::setAdaptor('Auth', $dbAdaptor);
        $this->setExpectedException('Exception', 'Something went wrong');

        $reset = new Auth_PasswordReset();
        $reset->insertPasswordResetData(self::SID_VALID, self::UUID_VALID);
    }

    // updatePasswordReset tests
    public function testUpdatePasswordReset()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_UPDATE);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_UPDATE, $this->once(), self::CALLED_WITH_UUID_SID);
        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $reset->updatePasswordReset(self::SID_VALID, self::UUID_VALID);
    }

    public function testUpdatePasswordResetThrowsError()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_UPDATE);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_UPDATE, $this->once(), [], true);
        Db_Manager::setAdaptor('Auth', $dbAdaptor);
        $this->setExpectedException('Exception', 'Something went wrong');

        $reset = new Auth_PasswordReset();
        $reset->updatePasswordReset(self::SID_VALID, self::UUID_VALID);
    }

    // getPasswordResetUuid tests
    public function testGetPasswordResetUuid()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_UUID);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_UUID, $this->once(), self::SID_VALID, false, self::UUID_VALID);
        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $result = $reset->getPasswordResetUuid(self::SID_VALID);
        $this->assertEquals(self::UUID_VALID, $result);
    }

    public function testGetPasswordResetUuidThrowsException()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_UUID);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_UUID, $this->once(), self::SID_VALID, true);
        Db_Manager::setAdaptor('Auth', $dbAdaptor);
        $this->setExpectedException('Exception', 'Something went wrong');

        $reset = new Auth_PasswordReset();
        $result = $reset->getPasswordResetUuid(self::SID_VALID);
        $this->assertNull($result);
    }

    // getResetRequestTimestamp tests
    public function testGetResetRequestTimestamp()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_TIMESTAMP);
        $this->setupDbMock(
            $dbAdaptor,
            self::STATEMENT_TIMESTAMP,
            $this->once(),
            self::CALLED_WITH_SID_UUID,
            false,
            self::UUID_TIMESTAMP_VALID
        );
        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $result = $reset->getResetRequestTimestamp(self::SID_VALID, self::UUID_VALID);
        $this->assertEquals(self::UUID_TIMESTAMP_VALID, $result);
    }

    public function testGetResetRequestTimestampThrowsException()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_TIMESTAMP);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_TIMESTAMP, $this->once(), self::CALLED_WITH_SID_UUID, true);
        Db_Manager::setAdaptor('Auth', $dbAdaptor);
        $this->setExpectedException('Exception', 'Something went wrong');

        $reset = new Auth_PasswordReset();
        $result = $reset->getResetRequestTimestamp(self::SID_VALID, self::UUID_VALID);
        $this->assertNull($result);
    }

    // removeRestRequestFromDb tests
    public function testRemoveRestRequestFromDb()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_DELETE);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_DELETE, $this->once(), self::CALLED_WITH_SID_UUID);
        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $reset->removeResetRequestFromDb(self::SID_VALID, self::UUID_VALID);
    }

    public function testRemoveRestRequestFromDbThrowsError()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_DELETE);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_DELETE, $this->once(), self::CALLED_WITH_SID_UUID, true);
        Db_Manager::setAdaptor('Auth', $dbAdaptor);
        $this->setExpectedException('Exception', 'Something went wrong');

        $reset = new Auth_PasswordReset();
        $reset->removeResetRequestFromDb(self::SID_VALID, self::UUID_VALID);
    }

    // storeOrUpdatePasswordResetDataWillUpdateData Happy paths
    public function testStoreOrUpdatePasswordResetDataWillUpdateData()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_UUID, self::STATEMENT_UPDATE, self::STATEMENT_INSERT);

        $this->setupDbMock($dbAdaptor, self::STATEMENT_UUID, $this->once(), self::SID_VALID, false, self::UUID_VALID);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_UPDATE, $this->once(), self::CALLED_WITH_UUID_SID);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_INSERT, $this->never());

        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $reset->storeOrUpdatePasswordResetData(self::SID_VALID, self::UUID_VALID);
    }

    public function testStoreOrUpdatePasswordResetDataWillCreateNewData()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_UUID, self::STATEMENT_UPDATE, self::STATEMENT_INSERT);

        $this->setupDbMock($dbAdaptor, self::STATEMENT_UUID, $this->once(), self::SID_VALID);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_INSERT, $this->once(), self::CALLED_WITH_SID_UUID);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_UPDATE, $this->never());

        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $reset->storeOrUpdatePasswordResetData(self::SID_VALID, self::UUID_VALID);
    }

    // storeOrUpdatePasswordResetData exception throwing paths
    public function testStoreOrUpdatePasswordResetDataWillThrowExceptionAtGetUuid()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_UUID, self::STATEMENT_UPDATE, self::STATEMENT_INSERT);

        $this->setupDbMock($dbAdaptor, self::STATEMENT_UUID, $this->once(), self::SID_VALID, true);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_INSERT, $this->never());
        $this->setupDbMock($dbAdaptor, self::STATEMENT_UPDATE, $this->never());

        $this->setExpectedException('Exception', 'Something went wrong');

        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $reset->storeOrUpdatePasswordResetData(self::SID_VALID, self::UUID_VALID);
    }

    public function testStoreOrUpdatePasswordResetDataWillThrowExceptionAtUpdate()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_UUID, self::STATEMENT_UPDATE, self::STATEMENT_INSERT);

        $this->setupDbMock($dbAdaptor, self::STATEMENT_UUID, $this->once(), self::SID_VALID, false, self::UUID_VALID);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_UPDATE, $this->once(), self::CALLED_WITH_SID_UUID, true);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_INSERT, $this->never());

        $this->setExpectedException('Exception', 'Something went wrong');

        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $reset->storeOrUpdatePasswordResetData(self::SID_VALID, self::UUID_VALID);
    }

    public function testStoreOrUpdatePasswordResetDataWillThrowExceptionAtInsert()
    {
        $dbAdaptor = $this->getDbMock(self::STATEMENT_UUID, self::STATEMENT_UPDATE, self::STATEMENT_INSERT);

        $this->setupDbMock($dbAdaptor, self::STATEMENT_UUID, $this->once(), self::SID_VALID, false);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_INSERT, $this->once(), self::CALLED_WITH_SID_UUID, true);
        $this->setupDbMock($dbAdaptor, self::STATEMENT_UPDATE, $this->never());

        $this->setExpectedException('Exception', 'Something went wrong');

        Db_Manager::setAdaptor('Auth', $dbAdaptor);

        $reset = new Auth_PasswordReset();
        $reset->storeOrUpdatePasswordResetData(self::SID_VALID, self::UUID_VALID);
    }
}
