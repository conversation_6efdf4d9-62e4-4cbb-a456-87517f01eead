<?php
/**
 * DirectDebitVerificationResponseData
 *
 * @package DirectDebitVerification
 * <AUTHOR> <marcin.<PERSON><PERSON><PERSON><PERSON><PERSON>@bt.com>
 */

namespace Plusnet\DirectDebitVerification\Model;

use Plusnet\DirectDebitVerification\Exceptions\DirectDebitVerificationException;
use Plusnet\DirectDebitVerification\Helpers\AuditLogHelper;
use Plusnet\DirectDebitVerification\Helpers\DataEncryptionHelper;
use Plusnet\DirectDebitVerification\Helpers\DirectDebitVerificationConfigHelper;

/**
 * Data object to store Direct Debit Verification response data
 *
 * @package    DirectDebitVerification
 * <AUTHOR> <marcin.mi<PERSON><PERSON><PERSON><PERSON>@bt.com>
 */
class DirectDebitVerificationResponseData extends DirectDebitData
{
    const DIRECT_DEBIT_VERIFICATION_TRANSACTION = 'DIRECT_DEBIT_VERIFICATION_TRANSACTION';

    public $validationHash;

    public $accountId;

    public $id;

    public $created;

    public $lastUpdated;

    public $stage;

    public $company;

    public $verificationStatus;

    public $currentAddressRecomendedStatus;

    public $formId;

    public $formTypeId;

    public $formType;

    public $title;

    public $firstName;

    public $middleName;

    public $lastName;

    public $dOB;

    public $email;

    public $mobile;

    public $applyingAsCompany;

    public $companyName;

    public $currentHouseNameNumber;

    public $currentStreet1;

    public $currentStreet2;

    public $currentTown;

    public $currentPostcode;

    public $currentCountry;

    public $bankAccountName;

    public $sortCode;

    public $accountNumber;

    public $ptxProfileName;

    public $ddDebtorReference;

    public $ddPlanAltReference;

    public $ddPlanReference;

    public $ddPlanSpecification;

    public $ddNoOfCollections;

    public $ddRegularAmount;

    public $ddFirstAmount;

    public $ddLastAmount;

    public $ddStartDate;

    public $ddPlanEndByDate;

    public $planEndByDate;

    public $planEndType;

    public $customData;

    public $formName;

    public $giftAid;

    public $bankName;

    public $bankAccountCreated;

    public $companyRegistrationNumber;

    public $nameScore;

    public $addressScore;

    public $proprietorScore;

    public $registrationMatch;

    public $accountStatus;

    public $namePassScore;

    public $addressPassScore;

    public $proprietorPassScore;

    public $registrationPassValue;

    public $companyNamePassScore;

    public $companyAddressPassScore;

    public $expirationTimer;

    public $verificationApplied;

    public $hasPassedValidation = false;

    /**
     * Rules to mask fields values when getting json representation of object - mainly for logging purposes
     * Fields (all optional):
     *   - pattern          - regex pattern to mask in callback payload received from BottomLine
     *   - unmaskedHeadSize - numer of characters that should be left unmasked on the beggining of value - when not
     *                        provided AuditLogHelper::DEFAULT_UNMASKED_HEAD_SIZE would be used
     *   - unmaskedTailSize - numer of characters that should be left unmasked on the end of value - when not provided
     *                        AuditLogHelper::DEFAULT_UNMASKED_TAIL_SIZE would be used
     *   - maskCharacter    - character that would be used to mask value - when not provided
     *                        AuditLogHelper::DEFAULT_MASKING_CHARACTER would be used
     * @var array
     */
    public static $maskingRules = [
        'firstName'              => [
            'pattern' => '/FirstName":"(.*?)"/'
        ],
        'middleName'             => [
            'pattern' => '/MiddleName":"(.*?)"/'
        ],
        'lastName'               => [
            'pattern' => '/LastName":"(.*?)"/'
        ],
        'email'                  => [
            'pattern' => '/Email":"(.*?)"/'
        ],
        'mobile'                 => [
            'pattern'          => '/Mobile":"(.*?)"/',
            'unmaskedHeadSize' => 1
        ],
        'companyName'            => [
            'pattern' => '/CompanyName":"(.*?)"/'
        ],
        'currentHouseNameNumber' => [
            'pattern' => '/CurrentHouseNameNumber":"(.*?)"/'
        ],
        'currentStreet1'         => [
            'pattern' => '/CurrentStreet1":"(.*?)"/'
        ],
        'currentStreet2'         => [
            'pattern' => '/CurrentStreet2":"(.*?)"/'
        ],
        'currentTown'            => [
            'pattern' => '/CurrentTown":"(.*?)"/'
        ],
        'currentPostcode'        => [
            'pattern'          => '/CurrentPostcode":"(.*?)"/',
            'unmaskedHeadSize' => 1,
            'unmaskedTailSize' => 1
        ],
        'currentCountry'         => [
            'pattern' => '/CurrentCountry":"(.*?)"/'
        ],
        'bankAccountName'        => [
            'pattern' => '/BankAccountName":"(.*?)"/'
        ],
        'sortCode'               => [
            'pattern'          => '/SortCode":"(.*?)"/',
            'unmaskedHeadSize' => 1,
            'unmaskedTailSize' => 1
        ],
        'accountNumber'          => [
            'pattern'          => '/AccountNumber":"(.*?)"/',
            'unmaskedHeadSize' => 1,
            'unmaskedTailSize' => 1
        ]
    ];

    protected static $hydrationMap = [
        'validationHash'                 => 'setValidationHash',
        'accountId'                      => 'setAccountId',
        'id'                             => 'setId',
        'created'                        => 'setCreated',
        'lastUpdated'                    => 'setLastUpdated',
        'stage'                          => 'setStage',
        'company'                        => 'setCompany',
        'verificationStatus'             => 'setVerificationStatus',
        'currentAddressRecomendedStatus' => 'setCurrentAddressRecomendedStatus',
        'formId'                         => 'setFormId',
        'formTypeId'                     => 'setFormTypeId',
        'formType'                       => 'setFormType',
        'title'                          => 'setTitle',
        'firstName'                      => 'setFirstName',
        'middleName'                     => 'setMiddleName',
        'lastName'                       => 'setLastName',
        'dOB'                            => 'setDOB',
        'email'                          => 'setEmail',
        'mobile'                         => 'setMobile',
        'applyingAsCompany'              => 'setApplyingAsCompany',
        'companyName'                    => 'setCompanyName',
        'currentHouseNameNumber'         => 'setCurrentHouseNameNumber',
        'currentStreet1'                 => 'setCurrentStreet1',
        'currentStreet2'                 => 'setCurrentStreet2',
        'currentTown'                    => 'setCurrentTown',
        'currentPostcode'                => 'setCurrentPostcode',
        'currentCountry'                 => 'setCurrentCountry',
        'bankAccountName'                => 'setBankAccountName',
        'sortCode'                       => 'setSortCode',
        'accountNumber'                  => 'setAccountNumber',
        'ptxProfileName'                 => 'setPtxProfileName',
        'ddDebtorReference'              => 'setDdDebtorReference',
        'ddPlanAltReference'             => 'setDdPlanAltReference',
        'ddPlanReference'                => 'setDdPlanReference',
        'ddPlanSpecification'            => 'setDdPlanSpecification',
        'ddNoOfCollections'              => 'setDdNoOfCollections',
        'ddRegularAmount'                => 'setDdRegularAmount',
        'ddFirstAmount'                  => 'setDdFirstAmount',
        'ddLastAmount'                   => 'setDdLastAmount',
        'ddStartDate'                    => 'setDdStartDate',
        'ddPlanEndByDate'                => 'setDdPlanEndByDate',
        'planEndByDate'                  => 'setPlanEndByDate',
        'planEndType'                    => 'setPlanEndType',
        'customData'                     => 'setCustomData',
        'formName'                       => 'setFormName',
        'giftAid'                        => 'setGiftAid',
        'bankName'                       => 'setBankName',
        'bankAccountCreated'             => 'setBankAccountCreated',
        'companyRegistrationNumber'      => 'setCompanyRegistrationNumber',
        'nameScore'                      => 'setNameScore',
        'addressScore'                   => 'setAddressScore',
        'proprietorScore'                => 'setProprietorScore',
        'registrationMatch'              => 'setRegistrationMatch',
        'accountStatus'                  => 'setAccountStatus',
        'namePassScore'                  => 'setNamePassScore',
        'addressPassScore'               => 'setAddressPassScore',
        'proprietorPassScore'            => 'setProprietorPassScore',
        'registrationPassValue'          => 'setRegistrationPassValue',
        'companyNamePassScore'           => 'setCompanyNamePassScore',
        'companyAddressPassScore'        => 'setCompanyAddressPassScore',
        'expirationTimer'                => 'setExpirationTimer',
        'verificationApplied'            => 'setVerificationApplied'
    ];

    /**
     * Get validationHash
     *
     * @return string
     */
    public function getValidationHash()
    {
        return $this->validationHash;
    }

    /**
     * Set validationHash
     *
     * @param string $validationHash validationHash
     *
     * @return void
     */
    public function setValidationHash($validationHash)
    {
        $this->validationHash = $validationHash;
    }

    /**
     * Get accountId
     *
     * @return int
     */
    public function getAccountId()
    {
        return $this->accountId;
    }

    /**
     * Set accountId
     *
     * @param string $accountId accountId
     *
     * @return void
     */
    public function setAccountId($accountId)
    {
        $this->accountId = $accountId;
    }

    /**
     * Get id
     *
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set id
     *
     * @param string $id id
     *
     * @return void
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * Get created
     *
     * @return string
     */
    public function getCreated()
    {
        return $this->created;
    }

    /**
     * Set created
     *
     * @param string $created created
     *
     * @return void
     */
    public function setCreated($created)
    {
        $this->created = $created;
    }

    /**
     * Get lastUpdated
     *
     * @return string
     */
    public function getLastUpdated()
    {
        return $this->lastUpdated;
    }

    /**
     * Set lastUpdated
     *
     * @param string $lastUpdated lastUpdated
     *
     * @return void
     */
    public function setLastUpdated($lastUpdated)
    {
        $this->lastUpdated = $lastUpdated;
    }

    /**
     * Get stage
     *
     * @return string
     */
    public function getStage()
    {
        return $this->stage;
    }

    /**
     * Set stage
     *
     * @param string $stage stage
     *
     * @return void
     */
    public function setStage($stage)
    {
        $this->stage = $stage;
    }

    /**
     * Get company
     *
     * @return string
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * Set company
     *
     * @param string $company company
     *
     * @return void
     */
    public function setCompany($company)
    {
        $this->company = $company;
    }

    /**
     * Get verificationStatus
     *
     * @return string
     */
    public function getVerificationStatus()
    {
        return $this->verificationStatus;
    }

    /**
     * Set verificationStatus
     *
     * @param string $verificationStatus verificationStatus
     *
     * @return void
     */
    public function setVerificationStatus($verificationStatus)
    {
        $this->verificationStatus = $verificationStatus;
    }

    /**
     * Get currentAddressRecomendedStatus
     *
     * @return string
     */
    public function getCurrentAddressRecomendedStatus()
    {
        return $this->currentAddressRecomendedStatus;
    }

    /**
     * Set currentAddressRecomendedStatus
     *
     * @param string $currentAddressRecomendedStatus currentAddressRecomendedStatus
     *
     * @return void
     */
    public function setCurrentAddressRecomendedStatus($currentAddressRecomendedStatus)
    {
        $this->currentAddressRecomendedStatus = $currentAddressRecomendedStatus;
    }

    /**
     * Get formId
     *
     * @return string
     */
    public function getFormId()
    {
        return $this->formId;
    }

    /**
     * Set formId
     *
     * @param string $formId formId
     *
     * @return void
     */
    public function setFormId($formId)
    {
        $this->formId = $formId;
    }

    /**
     * Get formTypeId
     *
     * @return string
     */
    public function getFormTypeId()
    {
        return $this->formTypeId;
    }

    /**
     * Set formTypeId
     *
     * @param string $formTypeId formTypeId
     *
     * @return void
     */
    public function setFormTypeId($formTypeId)
    {
        $this->formTypeId = $formTypeId;
    }

    /**
     * Get formType
     *
     * @return string
     */
    public function getFormType()
    {
        return $this->formType;
    }

    /**
     * Set formType
     *
     * @param string $formType formType
     *
     * @return void
     */
    public function setFormType($formType)
    {
        $this->formType = $formType;
    }

    /**
     * Get title
     *
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Set title
     *
     * @param string $title title
     *
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * Get firstName
     *
     * @return string
     */
    public function getFirstName()
    {
        return $this->firstName;
    }

    /**
     * Get validated FirstName
     *
     * @return \Val_Name|array
     */
    public function getValidatedFirstName()
    {
        return \Val_Name::getValidated($this->getFirstName());
    }

    /**
     * Set firstName
     *
     * @param string $firstName firstName
     *
     * @return void
     */
    public function setFirstName($firstName)
    {
        $this->firstName = $firstName;
    }

    /**
     * Get middleName
     *
     * @return string
     */
    public function getMiddleName()
    {
        return $this->middleName;
    }

    /**
     * Set middleName
     *
     * @param string $middleName middleName
     *
     * @return void
     */
    public function setMiddleName($middleName)
    {
        $this->middleName = $middleName;
    }

    /**
     * Get lastName
     *
     * @return string
     */
    public function getLastName()
    {
        return $this->lastName;
    }

    /**
     * Get validated LastName
     *
     * @return \Val_Name|array
     */
    public function getValidatedLastName()
    {
        return \Val_Name::getValidated($this->getLastName());
    }

    /**
     * Set lastName
     *
     * @param string $lastName lastName
     *
     * @return void
     */
    public function setLastName($lastName)
    {
        $this->lastName = $lastName;
    }

    /**
     * Get dOB
     *
     * @return string
     */
    public function getDOB()
    {
        return $this->dOB;
    }

    /**
     * Set dOB
     *
     * @param string $dOB dOB
     *
     * @return void
     */
    public function setDOB($dOB)
    {
        $this->dOB = $dOB;
    }

    /**
     * Get email
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Set email
     *
     * @param string $email email
     *
     * @return void
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }

    /**
     * Get mobile
     *
     * @return string
     */
    public function getMobile()
    {
        return $this->mobile;
    }

    /**
     * Set mobile
     *
     * @param string $mobile mobile
     *
     * @return void
     */
    public function setMobile($mobile)
    {
        $this->mobile = $mobile;
    }

    /**
     * Get applyingAsCompany
     *
     * @return string
     */
    public function getApplyingAsCompany()
    {
        return $this->applyingAsCompany;
    }

    /**
     * Set applyingAsCompany
     *
     * @param string $applyingAsCompany applyingAsCompany
     *
     * @return void
     */
    public function setApplyingAsCompany($applyingAsCompany)
    {
        $this->applyingAsCompany = $applyingAsCompany;
    }

    /**
     * Get companyName
     *
     * @return string
     */
    public function getCompanyName()
    {
        return $this->companyName;
    }

    /**
     * Set companyName
     *
     * @param string $companyName companyName
     *
     * @return void
     */
    public function setCompanyName($companyName)
    {
        $this->companyName = $companyName;
    }

    /**
     * Get currentHouseNameNumber
     *
     * @return string
     */
    public function getCurrentHouseNameNumber()
    {
        return $this->currentHouseNameNumber;
    }

    /**
     * Set currentHouseNameNumber
     *
     * @param string $currentHouseNameNumber currentHouseNameNumber
     *
     * @return void
     */
    public function setCurrentHouseNameNumber($currentHouseNameNumber)
    {
        $this->currentHouseNameNumber = $currentHouseNameNumber;
    }

    /**
     * Get currentStreet1
     *
     * @return string
     */
    public function getCurrentStreet1()
    {
        return $this->currentStreet1;
    }

    /**
     * Set currentStreet1
     *
     * @param string $currentStreet1 currentStreet1
     *
     * @return void
     */
    public function setCurrentStreet1($currentStreet1)
    {
        $this->currentStreet1 = $currentStreet1;
    }

    /**
     * Get currentStreet2
     *
     * @return string
     */
    public function getCurrentStreet2()
    {
        return $this->currentStreet2;
    }

    /**
     * Set currentStreet2
     *
     * @param string $currentStreet2 currentStreet2
     *
     * @return void
     */
    public function setCurrentStreet2($currentStreet2)
    {
        $this->currentStreet2 = $currentStreet2;
    }

    /**
     * Get currentTown
     *
     * @return string
     */
    public function getCurrentTown()
    {
        return $this->currentTown;
    }

    /**
     * Set currentTown
     *
     * @param string $currentTown currentTown
     *
     * @return void
     */
    public function setCurrentTown($currentTown)
    {
        $this->currentTown = $currentTown;
    }

    /**
     * Get currentPostcode
     *
     * @return string
     */
    public function getCurrentPostcode()
    {
        return $this->currentPostcode;
    }

    /**
     * Set currentPostcode
     *
     * @param string $currentPostcode currentPostcode
     *
     * @return void
     */
    public function setCurrentPostcode($currentPostcode)
    {
        $this->currentPostcode = $currentPostcode;
    }

    /**
     * Get currentCountry
     *
     * @return string
     */
    public function getCurrentCountry()
    {
        return $this->currentCountry;
    }

    /**
     * Set currentCountry
     *
     * @param string $currentCountry currentCountry
     *
     * @return void
     */
    public function setCurrentCountry($currentCountry)
    {
        $this->currentCountry = $currentCountry;
    }

    /**
     * Get bankAccountName
     *
     * @return string
     */
    public function getBankAccountName()
    {
        return $this->bankAccountName;
    }

    /**
     * Set bankAccountName
     *
     * @param string $bankAccountName bankAccountName
     *
     * @return void
     */
    public function setBankAccountName($bankAccountName)
    {
        $this->bankAccountName = $bankAccountName;
    }

    /**
     * Get sortCode
     *
     * @return string
     */
    public function getSortCode()
    {
        return $this->sortCode;
    }

    /**
     * Get validated SortCode
     *
     * @return \Val_DirectDebit_SortCode|array
     */
    public function getValidatedSortCode()
    {
        $validatedSortCode = array();
        $splittedSortCode = str_split($this->getSortCode(), 2);
        if (is_array($splittedSortCode) && sizeof($splittedSortCode) == 3) {
            $validatedSortCode = \Val_DirectDebit_SortCode::getValidated(
                $splittedSortCode[0],
                $splittedSortCode[1],
                $splittedSortCode[2]
            );
        }

        return $validatedSortCode;
    }

    /**
     * Set sortCode
     *
     * @param string $sortCode sortCode
     *
     * @return void
     */
    public function setSortCode($sortCode)
    {
        $this->sortCode = $sortCode;
    }

    /**
     * Get accountNumber
     *
     * @return string
     */
    public function getAccountNumber()
    {
        return $this->accountNumber;
    }

    /**
     * Get validated AccountNumber
     *
     * @return \Val_DirectDebit_AccountNumber|array
     */
    public function getValidatedAccountNumber()
    {
        return \Val_DirectDebit_AccountNumber::getValidated($this->getAccountNumber());
    }

    /**
     * Set accountNumber
     *
     * @param string $accountNumber accountNumber
     *
     * @return void
     */
    public function setAccountNumber($accountNumber)
    {
        $this->accountNumber = $accountNumber;
    }

    /**
     * Get ptxProfileName
     *
     * @return string
     */
    public function getPtxProfileName()
    {
        return $this->ptxProfileName;
    }

    /**
     * Set ptxProfileName
     *
     * @param string $ptxProfileName ptxProfileName
     *
     * @return void
     */
    public function setPtxProfileName($ptxProfileName)
    {
        $this->ptxProfileName = $ptxProfileName;
    }

    /**
     * Get ddDebtorReference
     *
     * @return string
     */
    public function getDdDebtorReference()
    {
        return $this->ddDebtorReference;
    }

    /**
     * Set ddDebtorReference
     *
     * @param string $ddDebtorReference ddDebtorReference
     *
     * @return void
     */
    public function setDdDebtorReference($ddDebtorReference)
    {
        $this->ddDebtorReference = $ddDebtorReference;
    }

    /**
     * Get ddPlanAltReference
     *
     * @return string
     */
    public function getDdPlanAltReference()
    {
        return $this->ddPlanAltReference;
    }

    /**
     * Set ddPlanAltReference
     *
     * @param string $ddPlanAltReference ddPlanAltReference
     *
     * @return void
     */
    public function setDdPlanAltReference($ddPlanAltReference)
    {
        $this->ddPlanAltReference = $ddPlanAltReference;
    }

    /**
     * Get ddPlanReference
     *
     * @return string
     */
    public function getDdPlanReference()
    {
        return $this->ddPlanReference;
    }

    /**
     * Set ddPlanReference
     *
     * @param string $ddPlanReference ddPlanReference
     *
     * @return void
     */
    public function setDdPlanReference($ddPlanReference)
    {
        $this->ddPlanReference = $ddPlanReference;
    }

    /**
     * Get ddPlanSpecification
     *
     * @return string
     */
    public function getDdPlanSpecification()
    {
        return $this->ddPlanSpecification;
    }

    /**
     * Set ddPlanSpecification
     *
     * @param string $ddPlanSpecification ddPlanSpecification
     *
     * @return void
     */
    public function setDdPlanSpecification($ddPlanSpecification)
    {
        $this->ddPlanSpecification = $ddPlanSpecification;
    }

    /**
     * Get ddNoOfCollections
     *
     * @return string
     */
    public function getDdNoOfCollections()
    {
        return $this->ddNoOfCollections;
    }

    /**
     * Set ddNoOfCollections
     *
     * @param string $ddNoOfCollections ddNoOfCollections
     *
     * @return void
     */
    public function setDdNoOfCollections($ddNoOfCollections)
    {
        $this->ddNoOfCollections = $ddNoOfCollections;
    }

    /**
     * Get ddRegularAmount
     *
     * @return string
     */
    public function getDdRegularAmount()
    {
        return $this->ddRegularAmount;
    }

    /**
     * Set ddRegularAmount
     *
     * @param string $ddRegularAmount ddRegularAmount
     *
     * @return void
     */
    public function setDdRegularAmount($ddRegularAmount)
    {
        $this->ddRegularAmount = $ddRegularAmount;
    }

    /**
     * Get ddFirstAmount
     *
     * @return string
     */
    public function getDdFirstAmount()
    {
        return $this->ddFirstAmount;
    }

    /**
     * Set ddFirstAmount
     *
     * @param string $ddFirstAmount ddFirstAmount
     *
     * @return void
     */
    public function setDdFirstAmount($ddFirstAmount)
    {
        $this->ddFirstAmount = $ddFirstAmount;
    }

    /**
     * Get ddLastAmount
     *
     * @return string
     */
    public function getDdLastAmount()
    {
        return $this->ddLastAmount;
    }

    /**
     * Set ddLastAmount
     *
     * @param string $ddLastAmount ddLastAmount
     *
     * @return void
     */
    public function setDdLastAmount($ddLastAmount)
    {
        $this->ddLastAmount = $ddLastAmount;
    }

    /**
     * Get ddStartDate
     *
     * @return string
     */
    public function getDdStartDate()
    {
        return $this->ddStartDate;
    }

    /**
     * Set ddStartDate
     *
     * @param string $ddStartDate ddStartDate
     *
     * @return void
     */
    public function setDdStartDate($ddStartDate)
    {
        $this->ddStartDate = $ddStartDate;
    }

    /**
     * Get ddPlanEndByDate
     *
     * @return string
     */
    public function getDdPlanEndByDate()
    {
        return $this->ddPlanEndByDate;
    }

    /**
     * Set ddPlanEndByDate
     *
     * @param string $ddPlanEndByDate ddPlanEndByDate
     *
     * @return void
     */
    public function setDdPlanEndByDate($ddPlanEndByDate)
    {
        $this->ddPlanEndByDate = $ddPlanEndByDate;
    }

    /**
     * Get planEndByDate
     *
     * @return string
     */
    public function getPlanEndByDate()
    {
        return $this->planEndByDate;
    }

    /**
     * Set planEndByDate
     *
     * @param string $planEndByDate planEndByDate
     *
     * @return void
     */
    public function setPlanEndByDate($planEndByDate)
    {
        $this->planEndByDate = $planEndByDate;
    }

    /**
     * Get planEndType
     *
     * @return string
     */
    public function getPlanEndType()
    {
        return $this->planEndType;
    }

    /**
     * Set planEndType
     *
     * @param string $planEndType planEndType
     *
     * @return void
     */
    public function setPlanEndType($planEndType)
    {
        $this->planEndType = $planEndType;
    }

    /**
     * Get customData
     *
     * @return string
     */
    public function getCustomData()
    {
        return $this->customData;
    }

    /**
     * Set customData
     *
     * @param string $customData customData
     *
     * @return void
     */
    public function setCustomData($customData)
    {
        $this->customData = $customData;
    }

    /**
     * Get formName
     *
     * @return string
     */
    public function getFormName()
    {
        return $this->formName;
    }

    /**
     * Set formName
     *
     * @param string $formName formName
     *
     * @return void
     */
    public function setFormName($formName)
    {
        $this->formName = $formName;
    }

    /**
     * Get giftAid
     *
     * @return string
     */
    public function getGiftAid()
    {
        return $this->giftAid;
    }

    /**
     * Set giftAid
     *
     * @param string $giftAid giftAid
     *
     * @return void
     */
    public function setGiftAid($giftAid)
    {
        $this->giftAid = $giftAid;
    }

    /**
     * Get bankName
     *
     * @return string
     */
    public function getBankName()
    {
        return $this->bankName;
    }

    /**
     * Set bankName
     *
     * @param string $bankName bankName
     *
     * @return void
     */
    public function setBankName($bankName)
    {
        $this->bankName = $bankName;
    }

    /**
     * Get bankAccountCreated
     *
     * @return string
     */
    public function getBankAccountCreated()
    {
        return $this->bankAccountCreated;
    }

    /**
     * Set bankAccountCreated
     *
     * @param string $bankAccountCreated bankAccountCreated
     *
     * @return void
     */
    public function setBankAccountCreated($bankAccountCreated)
    {
        $this->bankAccountCreated = $bankAccountCreated;
    }

    /**
     * Get companyRegistrationNumber
     *
     * @return string
     */
    public function getCompanyRegistrationNumber()
    {
        return $this->companyRegistrationNumber;
    }

    /**
     * Set companyRegistrationNumber
     *
     * @param string $companyRegistrationNumber companyRegistrationNumber
     *
     * @return void
     */
    public function setCompanyRegistrationNumber($companyRegistrationNumber)
    {
        $this->companyRegistrationNumber = $companyRegistrationNumber;
    }

    /**
     * Get nameScore
     *
     * @return string
     */
    public function getNameScore()
    {
        return $this->nameScore;
    }

    /**
     * Set nameScore
     *
     * @param string $nameScore nameScore
     *
     * @return void
     */
    public function setNameScore($nameScore)
    {
        $this->nameScore = $nameScore;
    }

    /**
     * Get addressScore
     *
     * @return string
     */
    public function getAddressScore()
    {
        return $this->addressScore;
    }

    /**
     * Set addressScore
     *
     * @param string $addressScore addressScore
     *
     * @return void
     */
    public function setAddressScore($addressScore)
    {
        $this->addressScore = $addressScore;
    }

    /**
     * Get proprietorScore
     *
     * @return string
     */
    public function getProprietorScore()
    {
        return $this->proprietorScore;
    }

    /**
     * Set proprietorScore
     *
     * @param string $proprietorScore proprietorScore
     *
     * @return void
     */
    public function setProprietorScore($proprietorScore)
    {
        $this->proprietorScore = $proprietorScore;
    }

    /**
     * Get registrationMatch
     *
     * @return string
     */
    public function getRegistrationMatch()
    {
        return $this->registrationMatch;
    }

    /**
     * Set registrationMatch
     *
     * @param string $registrationMatch registrationMatch
     *
     * @return void
     */
    public function setRegistrationMatch($registrationMatch)
    {
        $this->registrationMatch = $registrationMatch;
    }

    /**
     * Get accountStatus
     *
     * @return string
     */
    public function getAccountStatus()
    {
        return $this->accountStatus;
    }

    /**
     * Set accountStatus
     *
     * @param string $accountStatus accountStatus
     *
     * @return void
     */
    public function setAccountStatus($accountStatus)
    {
        $this->accountStatus = $accountStatus;
    }

    /**
     * Get namePassScore
     *
     * @return string
     */
    public function getNamePassScore()
    {
        return $this->namePassScore;
    }

    /**
     * Set namePassScore
     *
     * @param string $namePassScore namePassScore
     *
     * @return void
     */
    public function setNamePassScore($namePassScore)
    {
        $this->namePassScore = $namePassScore;
    }

    /**
     * Get addressPassScore
     *
     * @return string
     */
    public function getAddressPassScore()
    {
        return $this->addressPassScore;
    }

    /**
     * Set addressPassScore
     *
     * @param string $addressPassScore addressPassScore
     *
     * @return void
     */
    public function setAddressPassScore($addressPassScore)
    {
        $this->addressPassScore = $addressPassScore;
    }

    /**
     * Get proprietorPassScore
     *
     * @return string
     */
    public function getProprietorPassScore()
    {
        return $this->proprietorPassScore;
    }

    /**
     * Set proprietorPassScore
     *
     * @param string $proprietorPassScore proprietorPassScore
     *
     * @return void
     */
    public function setProprietorPassScore($proprietorPassScore)
    {
        $this->proprietorPassScore = $proprietorPassScore;
    }

    /**
     * Get registrationPassValue
     *
     * @return string
     */
    public function getRegistrationPassValue()
    {
        return $this->registrationPassValue;
    }

    /**
     * Set registrationPassValue
     *
     * @param string $registrationPassValue registrationPassValue
     *
     * @return void
     */
    public function setRegistrationPassValue($registrationPassValue)
    {
        $this->registrationPassValue = $registrationPassValue;
    }

    /**
     * Get companyNamePassScore
     *
     * @return string
     */
    public function getCompanyNamePassScore()
    {
        return $this->companyNamePassScore;
    }

    /**
     * Set companyNamePassScore
     *
     * @param string $companyNamePassScore companyNamePassScore
     *
     * @return void
     */
    public function setCompanyNamePassScore($companyNamePassScore)
    {
        $this->companyNamePassScore = $companyNamePassScore;
    }

    /**
     * Get companyAddressPassScore
     *
     * @return string
     */
    public function getCompanyAddressPassScore()
    {
        return $this->companyAddressPassScore;
    }

    /**
     * Set companyAddressPassScore
     *
     * @param string $companyAddressPassScore companyAddressPassScore
     *
     * @return void
     */
    public function setCompanyAddressPassScore($companyAddressPassScore)
    {
        $this->companyAddressPassScore = $companyAddressPassScore;
    }

    /**
     * Get expirationTimer
     *
     * @return string
     */
    public function getExpirationTimer()
    {
        return $this->expirationTimer;
    }

    /**
     * Set expirationTimer
     *
     * @param string $expirationTimer expirationTimer
     *
     * @return void
     */
    public function setExpirationTimer($expirationTimer)
    {
        $this->expirationTimer = $expirationTimer;
    }

    /**
     * Get verificationApplied
     *
     * @return bool
     */
    public function getVerificationApplied()
    {
        return $this->verificationApplied;
    }

    /**
     * Set verificationApplied
     *
     * @param string $verificationApplied verificationApplied
     *
     * @return void
     */
    public function setVerificationApplied($verificationApplied)
    {
        $this->verificationApplied = json_decode(strtolower($verificationApplied));
    }

    /**
     * Get verificationApplied
     *
     * @return bool
     */
    public function getHasPassedValidation()
    {
        return $this->hasPassedValidation;
    }

    /**
     * Calculate and set hasPassedValidation
     *
     * @return void
     */
    public function setHasPassedValidation()
    {
        $this->hasPassedValidation = $this->haveDetailsPassedValidation();
    }

    /**
     * Save object to database and return number of affected rows (shall be 1 always)
     *
     * @return int   Number of affected rows
     */
    public function saveToDatabase()
    {
        $this->setHasPassedValidation();

        $dataEncryptionHelper = DataEncryptionHelper::getInstance();

        \Db_Manager::setUseMaster(true);

        $db = \Db_Manager::getAdaptor(
            "DirectDebitVerification",
            self::DIRECT_DEBIT_VERIFICATION_TRANSACTION,
            true
        );
        $db->saveDDVerificationResponseToDatabase(
            $dataEncryptionHelper->encrypt($this->getData()),
            $this->getVerificationApplied(),
            $this->getHasPassedValidation(),
            $this->validationHash,
            $this->accountId
        );
        $affectedRows = $db->getAffectedRows();
        $this->commitTransaction();

        AuditLogHelper::getInstance()->log(
            sprintf(
                'DirectDebitVerificationResponseData=%s for validationHash=%s and accountId=%s stored in database '
                . 'successfully.',
                $this->toJson(),
                $this->validationHash,
                $this->accountId
            )
        );

        return $affectedRows;
    }

    /**
     * Get base64 encoded, serialized data representation of this object
     *
     * @return string
     */
    public function getData()
    {
        return base64_encode(serialize($this));
    }

    /**
     * Commit the db transaction
     *
     * @return void
     */
    protected function commitTransaction()
    {
        \Db_Manager::commit(self::DIRECT_DEBIT_VERIFICATION_TRANSACTION);
    }

    /**
     * Have DirectDebit details passed verification
     *
     * @return bool
     */
    public function haveDetailsPassedValidation()
    {
        /** @var $ddvConfig DirectDebitVerificationConfig */
        $ddvConfig = DirectDebitVerificationConfigHelper::get();

        return $this->getVerificationApplied() === true &&
            $this->getNameScore() >= $ddvConfig->getPassingNameScore() &&
            $this->getAddressScore() >= $ddvConfig->getPassingAddressScore();
    }

    /**
     * Instantiate object and hydrate it with data from provided array
     *
     * @param array $responseData Provided data
     *
     * @return DirectDebitVerificationResponseData
     */
    public static function fromArray($responseData)
    {
        if (!is_array($responseData)) {
            throw new DirectDebitVerificationException(
                sprintf(
                    'ResponseData provided to %s is not an array (responseData=%s)!',
                    __METHOD__,
                    var_export($responseData, 1)
                )
            );
        }

        $instance = new self();

        foreach (self::$hydrationMap as $key => $setter) {
            if (isset($responseData[ucwords($key)])) {
                $instance->$setter($responseData[ucwords($key)]);
            }
        }
        $instance->setHasPassedValidation();

        return $instance;
    }
}
