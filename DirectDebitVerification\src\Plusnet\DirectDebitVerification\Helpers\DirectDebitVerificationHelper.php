<?php
/**
 * Class DirectDebitVerificationHelper
 *
 * <AUTHOR> <marcin.<PERSON><PERSON><PERSON><PERSON><PERSON>@bt.com>
 */

namespace Plusnet\DirectDebitVerification\Helpers;

use Plusnet\BillingApiClient\Entity\Constant\PaymentMethodType;
use Plusnet\BillingApiClient\Entity\DirectDebit;
use Plusnet\BillingApiClient\Entity\PaymentDetails;
use Plusnet\BillingApiClient\Facade\BillingApiFacade;
use Plusnet\BillingApiClient\Service\ServiceManager;
use Plusnet\DirectDebitVerification\Model\DirectDebitData;

class DirectDebitVerificationHelper
{
    const DIRECT_DEBIT_VERIFICATION_TRANSACTION = 'DIRECT_DEBIT_VERIFICATION_TRANSACTION';

    const REFERENCE_PREFIX_PLUSNET = 'PNET-';
    const REFERENCE_PREFIX_JOHNLEWIS = 'JOHN';
    const REFERENCE_PREFIX_PARTNER = 'PART';

    /**
     * BillingApi client
     *
     * @var BillingApiFacade
     */
    private $billingApiClient;

    /**
     * Get DirectDebit reference prefix for isp
     *
     * @param string $isp Isp to get reference prefix for
     *
     * @return string
     */
    public static function getDirectDebitReferencePrefixForIsp($isp)
    {
        switch ($isp) {
            case 'johnlewis':
                $referencePrefix = self::REFERENCE_PREFIX_JOHNLEWIS;
                break;
            case 'partner':
                $referencePrefix = self::REFERENCE_PREFIX_PARTNER;
                break;
            default:
                $referencePrefix = self::REFERENCE_PREFIX_PLUSNET;
                break;
        }

        return $referencePrefix;
    }

    /**
     * Get BillingApi
     *
     * @return BillingApiFacade
     */
    public function getBillingApiClient()
    {
        if ($this->billingApiClient == null) {
            $this->billingApiClient = ServiceManager::getService('BillingApiFacade');
        }

        return $this->billingApiClient;
    }

    /**
     * Get isp from service id.
     *
     * @param integer $serviceId The service id
     *
     * @return string
     */
    protected function getIspFromServiceId($serviceId)
    {
        $db = \Db_Manager::getAdaptor('DirectDebitVerification');
        $isp = $db->getIspFromServiceId($serviceId);

        return $isp;
    }

    /**
     * Register DirectDebit in RBM
     *
     * @param int   $serviceId Service ID
     * @param array $data      DirectDebit details
     *
     * @return void
     */
    public function registerDirectDebitInBillingEngine($serviceId, $data)
    {
        $billingApiClient = $this->getBillingApiClient();

        $paymentDetails = new PaymentDetails();

        $isp = $this->getIspFromServiceId($serviceId);

        $fullName = $data["directDebitFirstName"] . " " . $data["directDebitSurname"];
        $directDebitCardPaymentMethod = new DirectDebit();
        $directDebitCardPaymentMethod->setName($fullName);
        $directDebitCardPaymentMethod->setSortCode($data["directDebitSortCode"]->__toString());
        $directDebitCardPaymentMethod->setAccountNumber($data["directDebitAccountNumber"]->__toString());
        $directDebitCardPaymentMethod->setReferenceNumber(
            self::getDirectDebitReferencePrefixForIsp($isp) . $serviceId
        );
        $directDebitCardPaymentMethod->setActive(true);

        $paymentDetails->addPaymentMethod(PaymentMethodType::DIRECT_DEBIT, $directDebitCardPaymentMethod);

        $paymentDetails->setOngoingPaymentMethod(ucwords(PaymentMethodType::DIRECT_DEBIT));

        try {
            $billingApiClient->registerPaymentDetails($serviceId, $paymentDetails);
            AuditLogHelper::getInstance()->log(
                sprintf(
                    'DirectDebit details for serviceId=%s registered in RBM successfully',
                    $serviceId
                )
            );
        } catch (\Exception $e) {
            AuditLogHelper::getInstance()->log(
                sprintf(
                    'Registration of new DirectDebit details for serviceId=%s in RBM failed due to: %s',
                    $serviceId,
                    $e->getMessage()
                ),
                \Log_LogData::LOG_LEVEL_ERROR
            );
        }
    }

    /**
     * Get Direct Debit Verification details from Dabatabase by validationHash
     *
     * @param string $validationHash Validation hash
     * @param bool   $log            Shall log in logs retrieved data - we don't want to log it in
     *                               ProgressRequirememt::valVerificationSuccessful where we call that method every
     *                               second until either callback from Bottomline return or validation reach max wait
     *                               time is reached
     *
     * @return array
     */
    public function getDirectDebitVerificationDetails($validationHash, $log = true)
    {
        $dbAdaptor = \Db_Manager::getAdaptor(
            "DirectDebitVerification",
            self::DIRECT_DEBIT_VERIFICATION_TRANSACTION,
            true
        );

        $directDebitVerificationDetails = $dbAdaptor->getDirectDebitVerificationDetails(
            $validationHash
        );

        \Db_Manager::closeTransactionConnections(self::DIRECT_DEBIT_VERIFICATION_TRANSACTION);

        if (is_array($directDebitVerificationDetails) && !empty($directDebitVerificationDetails)) {
            $dataEncryptionHelper = DataEncryptionHelper::getInstance();
            $directDebitVerificationDetails =
                $dataEncryptionHelper->returnDecryptedDdvCallbackData($directDebitVerificationDetails);

            $ddVerificationRequestData = unserialize(
                base64_decode(
                    $directDebitVerificationDetails['verificationRequestData']
                )
            );
            $ddVerificationResponseData = unserialize(
                base64_decode(
                    $directDebitVerificationDetails['verificationResponseData']
                )
            );

            if ($log) {
                AuditLogHelper::getInstance()->log(
                    sprintf(
                        'Retrieved following DirectDebitVerification details for validationHash=%s: '
                        . 'accountId=%s, verificationRequested=%s, verificationRequestData=%s, '
                        . 'verificationResponseData=%s, verificationApplied=%s, verificationCompleted=%s, '
                        . 'verificationSuccessful=%s, lastUpdated=%s ',
                        $validationHash,
                        $directDebitVerificationDetails['accountId'],
                        $directDebitVerificationDetails['verificationRequested'],
                        ($ddVerificationRequestData instanceof DirectDebitData)
                            ? $ddVerificationRequestData->toJson() : '',
                        ($ddVerificationResponseData instanceof DirectDebitData)
                            ? $ddVerificationResponseData->toJson() : '',
                        $directDebitVerificationDetails['verificationApplied'],
                        $directDebitVerificationDetails['verificationCompleted'],
                        $directDebitVerificationDetails['verificationSuccessful'],
                        $directDebitVerificationDetails['lastUpdated']
                    )
                );
            }
        }

        return $directDebitVerificationDetails;
    }

    /**
     * Get DirectDebitVerificationResponseData by validationHash
     *
     * @param string $validationHash ValidationHash
     *
     * @return \Plusnet\DirectDebitVerification\Model\DirectDebitVerificationResponseData|false
     */
    public function getDirectDebitVerificationResponseDataByValidationHash($validationHash)
    {
        $directDebitVerificationResponseData = false;
        $directDebitVerificationDetails = $this->getDirectDebitVerificationDetails($validationHash);
        if (!empty($directDebitVerificationDetails) && is_array($directDebitVerificationDetails)
            && isset($directDebitVerificationDetails['verificationResponseData'])
        ) {
            $directDebitVerificationResponseData = unserialize(
                base64_decode(
                    $directDebitVerificationDetails['verificationResponseData']
                )
            );
        }

        return $directDebitVerificationResponseData;
    }
}
