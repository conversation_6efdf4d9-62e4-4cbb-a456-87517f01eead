<?php
/**
 * DirectDebitVerificationReqTest
 *
 * @uses       TestCaseWithProxy
 * @package    DirectDebitVerification
 * @subpackage Tests
 * <AUTHOR> <marcin.<PERSON><PERSON><PERSON><PERSON><PERSON>@bt.com>
 */

namespace Plusnet\DirectDebitVerification\Test\Helpers;

use Plusnet\BillingApiClient\Entity\Constant\PaymentMethodType;
use Plusnet\BillingApiClient\Entity\DirectDebit;
use Plusnet\BillingApiClient\Entity\PaymentDetails;
use Plusnet\BillingApiClient\Facade\Adapter\QueueAdapter;
use Plusnet\BillingApiClient\Service\ServiceManager;
use Plusnet\DirectDebitVerification\Helpers\AuditLogHelper;
use Plusnet\DirectDebitVerification\Helpers\DataEncryptionHelper;
use Plusnet\DirectDebitVerification\Helpers\DirectDebitVerificationConfigHelper;
use Plusnet\DirectDebitVerification\Helpers\DirectDebitVerificationHelper;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationRequestData;
use Plusnet\DirectDebitVerification\Test\Model\TestConfig;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationResponseData;

class DirectDebitVerificationHelperTest extends \PHPUnit_Framework_TestCase
{
    const DIRECT_DEBIT_VERIFICATION_TRANSACTION = 'DIRECT_DEBIT_VERIFICATION_TRANSACTION';
    const VALIDATION_HASH = '8eb397eb4dbc8933d975e87e2b816af7';
    const ACCOUNT_ID = 2558172;
    const SERVICE_ID = 2486523;
    const ISP = 'plus.net';
    const SORT_CODE = [12, 23, 34];
    const ACCOUNT_NUMBER = ********;
    const FIRST_NAME = 'Mr.';
    const SURNAME = 'Topsy-Turvy';

    /**
     * Tear down
     *
     * @return void
     */
    public function tearDown()
    {
        \Db_Manager::reset();
        AuditLogHelper::clearInstance();
        DirectDebitVerificationConfigHelper::reset();
    }

    /**
     * Test getDirectDebitReferencePrefixForIsp works properly
     *
     * @return void
     */
    public function testGetDirectDebitReferencePrefixForIspWorksProperly()
    {
        $this->assertEquals(
            DirectDebitVerificationHelper::REFERENCE_PREFIX_JOHNLEWIS,
            DirectDebitVerificationHelper::getDirectDebitReferencePrefixForIsp(
                'johnlewis'
            )
        );
        $this->assertEquals(
            DirectDebitVerificationHelper::REFERENCE_PREFIX_PARTNER,
            DirectDebitVerificationHelper::getDirectDebitReferencePrefixForIsp(
                'partner'
            )
        );
        $this->assertEquals(
            DirectDebitVerificationHelper::REFERENCE_PREFIX_PLUSNET,
            DirectDebitVerificationHelper::getDirectDebitReferencePrefixForIsp(
                self::ISP
            )
        );
        $this->assertEquals(
            DirectDebitVerificationHelper::REFERENCE_PREFIX_PLUSNET,
            DirectDebitVerificationHelper::getDirectDebitReferencePrefixForIsp(
                'anthing else really so by default'
            )
        );
    }

    /**
     * Test getDirectDebitVerificationDetails works properly
     *
     * @return void
     */
    public function testGetDirectDebitVerificationDetailsWorksProperly()
    {
        $requested = date('Y-m-d H:i:s');
        $testConfigFile = TestConfig::LOCAL_TEST_CONFIG_FILE;
        DirectDebitVerificationConfigHelper::get($testConfigFile);

        $dataEncHelper = DataEncryptionHelper::getInstance();

        $expectedDdvData = [
            'id'                       => 1,
            'validationHash'           => self::VALIDATION_HASH,
            'accountId'                => self::ACCOUNT_ID,
            'verificationRequested'    => $requested,
            'verificationRequestData'  => base64_encode(
                serialize(
                    new DirectDebitVerificationRequestData()
                )
            ),
            'verificationResponseData' => null,
            'verificationApplied'      => null,
            'verificationCompleted'    => null,
            'verificationSuccessful'   => null,
            'lastUpdated'              => $requested,
        ];

        $mockDbAdaptor = $this->getMockBuilder(\Db_Adaptor::class)
            ->setMethods(['getDirectDebitVerificationDetails'])
            ->setConstructorArgs(
                [
                    'DirectDebitVerification',
                    self::DIRECT_DEBIT_VERIFICATION_TRANSACTION,
                    true
                ]
            )
            ->getMock();

        $mockDbAdaptor->expects($this->once())
            ->method('getDirectDebitVerificationDetails')
            ->with(self::VALIDATION_HASH)
            ->willReturn(
                [
                    'id'                       => 1,
                    'validationHash'           => self::VALIDATION_HASH,
                    'accountId'                => self::ACCOUNT_ID,
                    'verificationRequested'    => $requested,
                    'verificationRequestData'  => $dataEncHelper->encrypt(
                        base64_encode(
                            serialize(
                                new DirectDebitVerificationRequestData()
                            )
                        )
                    ),
                    'verificationResponseData' => null,
                    'verificationApplied'      => null,
                    'verificationCompleted'    => null,
                    'verificationSuccessful'   => null,
                    'lastUpdated'              => $requested,
                ]
            );

        \Db_Manager::setAdaptor(
            'DirectDebitVerification',
            $mockDbAdaptor,
            self::DIRECT_DEBIT_VERIFICATION_TRANSACTION
        );

        $mockLogHelper = $this->getMockBuilder(AuditLogHelper::class)
            ->setMethods(['log'])
            ->disableOriginalConstructor()
            ->getMock();

        AuditLogHelper::setInstance($mockLogHelper);

        $helper = new DirectDebitVerificationHelper();

        $this->assertEquals($expectedDdvData, $helper->getDirectDebitVerificationDetails(self::VALIDATION_HASH));
    }

    /**
     * Test registerDirectDebitInBillingEngine works properly
     *
     * @param bool $successfullyRegistered Registration successful flag
     *
     * @dataProvider provideDataForTestRegisterDirectDebitInBillingEngineWorksProperly
     *
     * @return void
     */
    public function testRegisterDirectDebitInBillingEngineWorksProperly($successfullyRegistered)
    {
        $data = [
            'directDebitSortCode'      => \Val_DirectDebit_SortCode::getValidated(
                self::SORT_CODE[0],
                self::SORT_CODE[1],
                self::SORT_CODE[2]
            ),
            'directDebitAccountNumber' => \Val_DirectDebit_AccountNumber::getValidated(self::ACCOUNT_NUMBER),
            'directDebitFirstName'     => self::FIRST_NAME,
            'directDebitSurname'       => self::SURNAME
        ];

        $paymentDetails = new PaymentDetails();

        $fullName = $data['directDebitFirstName'] . ' ' . $data['directDebitSurname'];
        $directDebitCardPaymentMethod = new DirectDebit();
        $directDebitCardPaymentMethod->setName($fullName);
        $directDebitCardPaymentMethod->setSortCode($data['directDebitSortCode']->__toString());
        $directDebitCardPaymentMethod->setAccountNumber($data['directDebitAccountNumber']->__toString());
        $directDebitCardPaymentMethod->setReferenceNumber(
            self::ISP . self::SERVICE_ID
        );
        $directDebitCardPaymentMethod->setActive(true);

        $paymentDetails->addPaymentMethod(PaymentMethodType::DIRECT_DEBIT, $directDebitCardPaymentMethod);

        $paymentDetails->setOngoingPaymentMethod(ucwords(PaymentMethodType::DIRECT_DEBIT));

        $mockAuditLogHelper = $this->getMockBuilder(AuditLogHelper::class)
            ->setMethods(['log'])
            ->disableOriginalConstructor()
            ->getMock();

        $mockBillingApiQueueAdapter = $this->getMockBuilder(QueueAdapter::class)
            ->setMethods(['registerPaymentDetails'])
            ->disableOriginalConstructor()
            ->getMock();

        if ($successfullyRegistered) {
            $mockBillingApiQueueAdapter->expects($this->once())
                ->method('registerPaymentDetails')
                ->with(
                    self::SERVICE_ID,
                    $this->callback(function ($subject) {
                        $paymentmethod = $subject->getPaymentMethods()[PaymentMethodType::DIRECT_DEBIT];

                        return $subject->getOngoingPaymentMethod() == ucwords(PaymentMethodType::DIRECT_DEBIT)
                            && $paymentmethod->getSortCode() == implode('-', self::SORT_CODE)
                            && $paymentmethod->getAccountNumber() == self::ACCOUNT_NUMBER
                            && $paymentmethod->getName() == self::FIRST_NAME . ' ' . self::SURNAME
                            && $paymentmethod->getReferenceNumber() == 'PNET-' . self::SERVICE_ID
                            && $paymentmethod->isActive() == true;
                    })
                );
            $logMessage = sprintf(
                'DirectDebit details for serviceId=%s registered in RBM successfully',
                self::SERVICE_ID
            );
        } else {
            $exceptionMessage = 'Attempt to register Paymentmethod in RBM failed!';
            $mockBillingApiQueueAdapter->expects($this->once())
                ->method('registerPaymentDetails')
                ->with(
                    self::SERVICE_ID,
                    $this->callback(function ($subject) {
                        $paymentmethod = $subject->getPaymentMethods()[PaymentMethodType::DIRECT_DEBIT];

                        return $subject->getOngoingPaymentMethod() == ucwords(PaymentMethodType::DIRECT_DEBIT)
                            && $paymentmethod->getSortCode() == implode('-', self::SORT_CODE)
                            && $paymentmethod->getAccountNumber() == self::ACCOUNT_NUMBER
                            && $paymentmethod->getName() == self::FIRST_NAME . ' ' . self::SURNAME
                            && $paymentmethod->getReferenceNumber() == 'PNET-' . self::SERVICE_ID
                            && $paymentmethod->isActive() == true;
                    })
                )
                ->willThrowException(new \Exception($exceptionMessage));

            $logMessage = sprintf(
                'Registration of new DirectDebit details for serviceId=%s in RBM failed due to: %s',
                self::SERVICE_ID,
                $exceptionMessage
            );
        }

        $mockAuditLogHelper->expects($this->once())
            ->method('log')
            ->with($logMessage);

        AuditLogHelper::setInstance($mockAuditLogHelper);

        ServiceManager::setService('BillingApiQueueAdapter', $mockBillingApiQueueAdapter);

        $mockDdvHelper = $this->getMockBuilder(DirectDebitVerificationHelper::class)
            ->setMethods(['getIspFromServiceId'])
            ->getMock();

        $mockDdvHelper->expects($this->once())
            ->method('getIspFromServiceId')
            ->with(self::SERVICE_ID)
            ->willReturn(self::ISP);


        $mockDdvHelper->registerDirectDebitInBillingEngine(self::SERVICE_ID, $data);

        ServiceManager::reset();
    }

    /**
     * Provide data for testRegisterDirectDebitInBillingEngine works properly
     *
     * @return array
     */
    public function provideDataForTestRegisterDirectDebitInBillingEngineWorksProperly()
    {
        return [
            'Successfully registered' => [
                '$successfullyRegistered' => true
            ],
            'Registration failed'     => [
                '$successfullyRegistered' => false
            ]
        ];
    }

    /**
     * Test getDirectDebitVerificationResponseDataByValidationHash works properly
     *
     * @param array                                     $ddvDetails     Direct Debit Verification details
     * @param DirectDebitVerificationResponseData|false $expectedResult Expected result
     *
     * @dataProvider provideDataForTestGetDirectDebitVerificationResponseDataByValidationHashWorksProperly
     *
     * @return void
     */
    public function testGetDirectDebitVerificationResponseDataByValidationHashWorksProperly(
        $ddvDetails,
        $expectedResult
    ) {
        $mockDdvHelper = $this->getMockBuilder(DirectDebitVerificationHelper::class)
            ->setMethods(['getDirectDebitVerificationDetails'])
            ->getMock();

        $mockDdvHelper->expects($this->once())
            ->method('getDirectDebitVerificationDetails')
            ->with(self::VALIDATION_HASH)
            ->willReturn($ddvDetails);

        $this->assertEquals(
            $expectedResult,
            $mockDdvHelper->getDirectDebitVerificationResponseDataByValidationHash(
                self::VALIDATION_HASH
            )
        );
    }

    /**
     * Provide data for testGetDirectDebitVerificationResponseDataByValidationHashWorksProperly
     *
     * @return array[]
     */
    public function provideDataForTestGetDirectDebitVerificationResponseDataByValidationHashWorksProperly()
    {
        $responseData = new DirectDebitVerificationResponseData();
        $responseData->validationHash = self::VALIDATION_HASH;

        return [
            'No DDV details - expect false'                       => [
                'ddvDetails'     => null,
                'expectedResult' => false
            ],
            'No response data in DDV details - expect false'      => [
                'ddvDetails'     => [],
                'expectedResult' => false
            ],
            'Invalid response data in DDV details - expect false' => [
                'ddvDetails'     => ['verificationResponseData' => 'response'],
                'expectedResult' => false
            ],
            'Valid response data in DDV details - expect false'   => [
                'ddvDetails'     => ['verificationResponseData' => base64_encode(serialize($responseData))],
                'expectedResult' => $responseData
            ]
        ];
    }
}
