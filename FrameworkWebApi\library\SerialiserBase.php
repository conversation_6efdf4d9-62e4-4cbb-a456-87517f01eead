<?php

/**
 * SerialiserBase
 *
 * base class for serialiser classes
 *
 * @package    FrameworkWebApi
 * @subpackage Library
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2012 Plusnet
 * @link       http://www.plus.net/
 */

$library = '/local/codebase2005/modules/FrameworkWebApi/library/';
include_once $library . 'Serialiser.GenericImmediatePaymentApplication_PaymentRequestData.php';
include_once $library . 'Serialiser.DirectDebitVerificationRequestData.php';
include_once $library . 'Serialiser.DirectDebitVerificationResponseData.php';

/**
 * SerialiserBase
 *
 * base class for serialiser classes
 *
 * @package    FrameworkWebApi
 * @subpackage Library
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright  2012 Plusnet
 * @link       http://www.plus.net/
 */
class SerialiserBase
{

    protected $className = null;
    protected $objectData = null;
    protected $object = null;

    /**
     * constructor
     *
     * @param array $objectData data needed to populate the generated object
     */
    protected function __construct($objectData)
    {
        $this->objectData = $objectData;
    }

    /**
     * populate object with data
     * must be implemented in child class
     *
     * @throws Exception
     *
     * @return void
     */
    protected function populateObject()
    {
        throw new Exception('Not Implemented');
    }

    /**
     * returns an object of type specified in $this->className
     * and populated with data from $this->objectData
     *
     * @return Object
     */
    private function _getObject()
    {
        $this->object = $this->_makeObject();
        if ($this->object !== null) {
            $this->populateObject();
        }
        return $this->object;
    }

    /**
     * returns an object of type specified in $this->className
     *
     * @return Object
     */
    private function _makeObject()
    {
        $returnObject = null;
        if (class_exists($this->className)) {
            $unknownObject = new $this->className();
            if ($unknownObject instanceof $this->className) {
                $returnObject = $unknownObject;
            }
        }
        return $returnObject;
    }

    /**
     * returns a base64 encoded serialised php object of type $className
     * populated with data in the $objectData array
     *
     * @param string $className  class name of object to be created
     * @param array  $objectData data to populate object with
     *
     * @return string
     */
    public static function getSerialisedObject($className, $objectData)
    {
        $serialiserClassName = 'Serialiser_' . $className;
        if (class_exists($serialiserClassName)) {
            $serialiserObject = new $serialiserClassName($objectData);
            $returnObject = $serialiserObject->_getObject();
            return base64_encode(serialize($returnObject));
        }
    }

    /**
     * decodes and deserialises a base64 encoded serialised php object
     * returns a json string representation of the object
     *
     * @param string $encodedString base64 encoded serialised php object
     *
     * @return string json string representation of the object
     */
    public static function getEncodedObjectAsJson($encodedString)
    {
        $returnString = '';
        try {
            if ($encodedString) {
                $serialisedObject = base64_decode($encodedString, true);
                if ($serialisedObject !== false) {
                    $object = unserialize($serialisedObject);
                    if ($object !== false) {
                        // Use PHP 5.6 compatible JSON encoding for PHP 8.2
                        if (PHP_VERSION_ID >= 80200) {
                            $jsonString = self::php56CompatibleJsonEncode($object);
                        } else {
                            $jsonString = json_encode($object);
                        }

                        if ($jsonString) {
                            $returnString = $jsonString;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            // we want to discard any exceptions in here thrown by unserialize and simply return ''
            error_log(sprintf('%s non fatal error : %s', __CLASS__, $e->getMessage()));
        }
        return $returnString;
    }

    /**
     * PHP 5.6 compatible JSON encoding for PHP 8.2
     * Ensures that JSON generated by PHP 8.2 can be parsed by PHP 5.6
     *
     * @param mixed $object Object to encode
     * @return string|false JSON string or false on error
     */
    public static function php56CompatibleJsonEncode($object)
    {
        // Clean the object for PHP 5.6 compatibility
        $cleanObject = self::cleanObjectForPhp56($object);

        // Use only PHP 5.6 compatible JSON flags
        $flags = JSON_UNESCAPED_SLASHES;

        // Encode with PHP 5.6 compatible settings
        $jsonString = json_encode($cleanObject, $flags);

        if ($jsonString === false) {
            // Fallback: try with no flags
            $jsonString = json_encode($cleanObject);
        }

        // Additional cleanup for PHP 5.6 compatibility
        if ($jsonString !== false) {
            $jsonString = self::cleanJsonForPhp56($jsonString);
        }

        return $jsonString;
    }

    /**
     * Clean object data to be compatible with PHP 5.6
     *
     * @param mixed $data Data to clean
     * @return mixed Cleaned data
     */
    private static function cleanObjectForPhp56($data)
    {
        if (is_string($data)) {
            // Ensure proper encoding
            if (!mb_check_encoding($data, 'UTF-8')) {
                $data = mb_convert_encoding($data, 'UTF-8', 'auto');
            }

            // Remove problematic characters that might cause issues in PHP 5.6
            $data = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $data);

            // Convert high Unicode characters to basic equivalents where possible
            $data = self::convertUnicodeToBasic($data);

            return $data;
        } elseif (is_array($data)) {
            $cleanArray = [];
            foreach ($data as $key => $value) {
                $cleanKey = self::cleanObjectForPhp56($key);
                $cleanValue = self::cleanObjectForPhp56($value);
                $cleanArray[$cleanKey] = $cleanValue;
            }
            return $cleanArray;
        } elseif (is_object($data)) {
            $cleanObject = clone $data;
            foreach (get_object_vars($cleanObject) as $key => $value) {
                $cleanObject->$key = self::cleanObjectForPhp56($value);
            }
            return $cleanObject;
        }

        return $data;
    }

    /**
     * Clean JSON string for PHP 5.6 compatibility
     *
     * @param string $json JSON string
     * @return string Cleaned JSON string
     */
    private static function cleanJsonForPhp56($json)
    {
        // Remove Unicode escape sequences that PHP 5.6 might not handle properly
        $json = preg_replace('/\\\\u([0-9a-fA-F]{4})/', '', $json);

        // Ensure basic ASCII characters only for critical parts
        $json = mb_convert_encoding($json, 'UTF-8', 'UTF-8');

        // Remove any remaining problematic characters
        $json = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $json);

        return $json;
    }

    /**
     * Convert high Unicode characters to basic ASCII equivalents
     *
     * @param string $text Text to convert
     * @return string Converted text
     */
    private static function convertUnicodeToBasic($text)
    {
        // Common Unicode to ASCII mappings using Unicode escape sequences
        $replacements = [
            // Smart quotes
            "\u201C" => '"', "\u201D" => '"', "\u2018" => "'", "\u2019" => "'",
            // Dashes
            "\u2013" => '-', "\u2014" => '-',
            // Other common characters
            "\u2026" => '...',
            "\u20AC" => 'EUR',
            "\u00A3" => 'GBP',
            "\u00A9" => '(c)',
            "\u00AE" => '(R)',
            "\u2122" => '(TM)',
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $text);
    }

    /**
     * set value if not null
     *
     * @param mixed $target item to set value on
     * @param mixed $value  value to set
     *
     * @return void
     */
    protected function setIfNotNull(&$target, &$value)
    {
        if (isset($value)) {
            $target = $value;
        }
    }
}
