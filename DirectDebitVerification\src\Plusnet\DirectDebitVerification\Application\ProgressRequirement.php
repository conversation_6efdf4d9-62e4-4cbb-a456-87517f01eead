<?php
/**
 * Class ProgressRequirement
 *
 * <AUTHOR> <marcin.<PERSON><PERSON><PERSON><PERSON><PERSON>@bt.com>
 */

namespace Plusnet\DirectDebitVerification\Application;

use Plusnet\DirectDebitVerification\Exceptions\DirectDebitVerificationException;
use Plusnet\DirectDebitVerification\Helpers\AuditLogHelper;
use Plusnet\DirectDebitVerification\Helpers\DirectDebitVerificationConfigHelper;
use Plusnet\DirectDebitVerification\Helpers\DirectDebitVerificationHelper;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationConfig;
use Plusnet\DirectDebitVerification\Model\DirectDebitVerificationResponseData;

class ProgressRequirement extends \Mvc_WizardRequirement
{
    public static $directDebitVerificationSuccessful = null;

    /**
     * Inputs for this requirement
     *
     * @var    array
     * @access protected
     */
    protected $arrInputs = array(
        'directDebitVerificationSuccessful' => "external:Custom"
    );

    /**
     * Get application state variable
     *
     * @param string $variableName Variable name
     *
     * @return false|mixed
     */
    public function getAppStateVariable($variableName)
    {
        return $this->getApplicationStateVariable($variableName);
    }

    /**
     * Validate verification was successful
     *
     * @param bool $directDebitVerificationSuccessful Is direct debit verification successful
     *
     * @return false[]
     */
    public function valVerificationSuccessful($directDebitVerificationSuccessful)
    {
        $validationHash = $this->getAppStateVariable('validationHash');

        $waitTime = 0;

        $directDebitVerificationHelper = new DirectDebitVerificationHelper();

        $validatedReturn = ['directDebitVerificationSuccessful' => false];

        try {
            $maxWaitTime = $this->getMaxWaitTimeSec();
            while (true) {
                if ($waitTime > $maxWaitTime) {
                    self::$directDebitVerificationSuccessful = false;
                    throw new DirectDebitVerificationException(
                        sprintf(
                            'DirectDebitVerification for validationHash=%s timed out - please try again.',
                            $validationHash
                        )
                    );
                }

                $directDebitVerificationDetails = $directDebitVerificationHelper->getDirectDebitVerificationDetails(
                    $validationHash,
                    false
                );

                $ddvResponseData = unserialize(
                    base64_decode($directDebitVerificationDetails['verificationResponseData'])
                );

                if ($ddvResponseData instanceof DirectDebitVerificationResponseData) {
                    if ($ddvResponseData->haveDetailsPassedValidation()) {
                        $validatedReturn['directDebitVerificationSuccessful'] = true;

                        $validatedReturn["directDebitAccountNumber"] = \Val_DirectDebit_AccountNumber::getValidated(
                            $ddvResponseData->accountNumber
                        );
                        $validatedReturn["directDebitSortCode"] = \Val_DirectDebit_SortCode::getValidated(
                            substr($ddvResponseData->sortCode, 0, 2),
                            substr($ddvResponseData->sortCode, 2, 2),
                            substr($ddvResponseData->sortCode, 4, 2)
                        );
                        $validatedReturn["directDebitSurname"] = \Val_Name::getValidated($ddvResponseData->lastName);
                        $validatedReturn["directDebitFirstName"] = \Val_Name::getValidated($ddvResponseData->firstName);
                        $validatedReturn["directDebitOffline"] = '';
                        self::$directDebitVerificationSuccessful = true;

                        AuditLogHelper::getInstance()->log(
                            sprintf(
                                'DirectDebitVerification for validationHash=%s passed verification succesfully '
                                . '(nameScore=%s, addressScore=%s).',
                                $validationHash,
                                $ddvResponseData->getNameScore(),
                                $ddvResponseData->getAddressScore()
                            )
                        );

                        break;
                    } else {
                        self::$directDebitVerificationSuccessful = false;
                        throw new DirectDebitVerificationException(
                            sprintf(
                                'DirectDebitVerification for validationHash=%s failed due to incorrect details passed'
                                . ' (nameScore=%s, addressScore=%s) - please try again.',
                                $validationHash,
                                $ddvResponseData->getNameScore(),
                                $ddvResponseData->getAddressScore()
                            )
                        );
                    }
                }
                sleep(1);
                $waitTime++;
            }
        } catch (DirectDebitVerificationException $e) {
            $patternsToRemove = [
                '/for validationHash=.*? /',
                '/\(nameScore=\d, addressScore=\d\) /'
            ];

            $validationError = $e->getMessage();
            foreach ($patternsToRemove as $pattern) {
                $validationError = preg_replace($pattern, '', $validationError);
            }

            $this->addValidationError(
                'DirectDebitVerification',
                'ERROR',
                $validationError
            );
        }

        return $validatedReturn;
    }

    /**
     * Get an idempotence id for the action on the page
     *
     * @param string $module Module Identifier
     * @param string $page   Page Identifier
     * @param string $action Action Identifier
     *
     * @return integer
     */
    protected function getIdempotenceId($module, $page, $action)
    {
        return \Mvc_SiteController::getIdempotenceId(
            $module,
            $page,
            $action,
            true
        );
    }

    /**
     * Describe
     *
     * @param array $data Bag of data
     *
     * @return array
     */
    public function describe(array &$data)
    {
        $data['directDebitVerificationSuccessful'] = self::$directDebitVerificationSuccessful;
        $return = [
            'intIdempotenceId'                  => $this->getIdempotenceId(
                'DirectDebitVerification',
                'Main',
                'submit',
                true
            ),
            'directDebitVerificationSuccessful' => self::$directDebitVerificationSuccessful,
            'directDebitValidationInProgress'   => (isset(self::$directDebitVerificationSuccessful))
                ? self::$directDebitVerificationSuccessful : $data['directDebitValidationInProgress'],
            'doNotVerifyDdDetailsToggleState'   => false
        ];

        return $return;
    }

    /**
     * Returns how long to wait for response via direct debit verification config helper which handles caching
     *
     * @return int
     */
    public function getMaxWaitTimeSec()
    {
        /** @var DirectDebitVerificationConfig $ddvConfig */
        $ddvConfig = DirectDebitVerificationConfigHelper::get();
        return $ddvConfig->getMaxWaitTimeSec();
    }
}
