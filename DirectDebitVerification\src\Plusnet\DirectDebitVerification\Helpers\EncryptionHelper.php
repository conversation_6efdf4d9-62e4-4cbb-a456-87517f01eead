<?php
/**
 * DirectDebitVerificationHelper class to assist with en/de-crypting data as part of Direct Debit Verification steps
 *
 * @package    DirectDebitVerification
 * <AUTHOR> <<EMAIL>>
 */

namespace Plusnet\DirectDebitVerification\Helpers;

/**
 * EncryptionHelper class
 *
 * @package DirectDebitVerification
 * <AUTHOR> <<EMAIL>>
 */
class EncryptionHelper
{
    const AES_256_CBC = 'aes-256-cbc';
    const SHA = 'sha256';

    /**
     * Holds the name of the encryption cipher that will be used
     *
     * @var string
     */
    private $cipher = null;

    /**
     * Constructor for EncryptionHelper - sets which cipher will be used
     *
     * @param string $cipher The encryption cipher to encode with (can use default)
     *                       - Should be an supported openssl cipher: see openssl_get_cipher_methods()
     */
    public function __construct($cipher = self::AES_256_CBC)
    {
        $this->cipher = $cipher;
    }

    /**
     * Encrypt a string using the provided key hash
     *
     * @param string $stringToEncrypt  The string that should be encrypted
     * @param string $sharedSecretHash Binary digest hash value of key to use
     *
     * @return string The encrypted string
     */
    public function encrypt($stringToEncrypt, $sharedSecretHash)
    {
        $encryptor = $this->getEncryptionObject($sharedSecretHash);
        return $encryptor->encrypt($stringToEncrypt);
    }

    /**
     * Decrypt a string using the provided key hash
     *
     * @param string $stringToDecrypt  The string that should be decrypted
     * @param string $sharedSecretHash Binary digest hash value of key to use
     *
     * @return string The decrypted string
     */
    public function decrypt($stringToDecrypt, $sharedSecretHash)
    {
        $encryptor = $this->getEncryptionObject($sharedSecretHash);
        return $encryptor->decrypt($stringToDecrypt);
    }

    /**
     * Getter for the cipher variable
     *
     * @return string
     */
    public function getCipher()
    {
        return $this->cipher;
    }

    /**
     * Returns an object to do the encryption configured in same way as DDV 3rd party supplier
     *
     * @param string $sharedSecretHash Binary hash of the key to use
     *
     * @return \EncryptMan_OpensslEncryption
     */
    public function getEncryptionObject($sharedSecretHash)
    {
        return new \EncryptMan_OpensslEncryption($sharedSecretHash, $this->getCipher(), false);
    }

    /**
     * Computes and returns a binary digest hashed value of the provided key that's the length of the algorithm selected
     *
     * Used to produce encryption keys of the max bit length for the cipher in use to ensure max security
     *     e.g. 256 bit key digest hash for 'aes-256-cbc' using 'sha256'
     *
     * @param string $key       The data: the secret key to compute a digest hash from
     * @param string $algorithm Which digest method algorithm to use
     *
     * @return string|false
     */
    public function hashSecret($key, $algorithm = self::SHA)
    {
        return openssl_digest($key, $algorithm, true);
    }
}
